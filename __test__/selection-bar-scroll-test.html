<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>划词Bar滚动定位测试 - 内部滚动容器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden; /* 禁用body滚动 */
        }

        .main-container {
            height: calc(100vh - 40px);
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid #E5E7EB;
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 20px;
            border-bottom: 1px solid #E5E7EB;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
        }

        .scrollable-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            /* 这是我们要测试的滚动容器 */
        }

        .content-section {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            min-height: 200px;
        }

        .test-text {
            line-height: 1.6;
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #ffeaa7;
        }

        .instructions {
            background: #e8f4fd;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }

        .instructions ol {
            margin-bottom: 0;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .mock-selection-bar {
            position: absolute;
            background: #4285F4;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            font-size: 14px;
            z-index: 1000;
            display: none;
            pointer-events: none;
            white-space: nowrap;
        }

        .scroll-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 2000;
            min-width: 200px;
        }

        .section-marker {
            position: absolute;
            left: -30px;
            top: 10px;
            background: #ff6b6b;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .content-section {
            position: relative;
        }

        .scroll-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        /* 自定义滚动条样式 */
        .scrollable-content::-webkit-scrollbar {
            width: 8px;
        }

        .scrollable-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .scrollable-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .scrollable-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="scrollIndicator">
        <strong>滚动监听状态</strong><br>
        容器滚动位置: <span id="containerScrollY">0</span>px<br>
        页面滚动位置: <span id="pageScrollY">0</span>px<br>
        滚动事件来源: <span id="scrollSource">无</span><br>
        滚动事件计数: <span id="scrollCount">0</span>
    </div>

    <div class="main-container">
        <div class="header">
            <h2>📋 划词Bar内部滚动容器测试</h2>
            <div class="scroll-info">
                <strong>测试说明：</strong>这个页面的滚动发生在内部的div容器中，而不是body或html元素上。
                我们要测试划词工具栏是否能正确响应内部容器的滚动事件。
            </div>
        </div>

        <div class="scrollable-content" id="scrollableContent">
            <div class="instructions">
                <h3>🧪 测试步骤</h3>
                <ol>
                    <li>选中下面任意一段文字，观察模拟的划词工具栏出现</li>
                    <li>在这个灰色区域内滚动，观察工具栏是否能跟随文本位置</li>
                    <li>注意右上角的滚动状态信息，确认滚动事件被正确捕获</li>
                    <li>测试在不同滚动位置选中文字的定位准确性</li>
                </ol>
            </div>

            <div class="content-section">
                <div class="section-marker">区域1</div>
                <h3>第一部分：顶部测试区域</h3>
                <p class="test-text">
                    这是第一段测试文本。<span class="highlight">请选中这段高亮文字来测试划词工具栏的定位功能。</span>
                    当您选中文字后，应该会看到一个蓝色的工具栏出现在选中文本的下方。
                </p>
                <p class="test-text">
                    这里有更多的文本内容用于测试。Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,
                    quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                </p>
                <p class="test-text">
                    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                    Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                </p>
            </div>

            <div class="content-section">
                <div class="section-marker">区域2</div>
                <h3>第二部分：中间测试区域</h3>
                <p class="test-text">
                    这是页面中间位置的测试文本。<span class="highlight">选中这段文字测试内部容器滚动后的定位准确性。</span>
                    在内部容器中滚动后再选中文字，工具栏应该能够正确定位到选中文本的相对位置。
                </p>
                <p class="test-text">
                    Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium,
                    totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
                </p>
                <p class="test-text">
                    Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni
                    dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet.
                </p>
            </div>

            <div class="content-section">
                <div class="section-marker">区域3</div>
                <h3>第三部分：下方测试区域</h3>
                <p class="test-text">
                    这是容器下方的测试文本。<span class="highlight">在容器底部选中文字时，工具栏应该显示在文本上方。</span>
                    这样可以确保工具栏始终在可视区域内，即使是在内部滚动容器中。
                </p>
                <p class="test-text">
                    At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti
                    atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.
                </p>
                <p class="test-text">
                    Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.
                    Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est eligendi optio.
                </p>
            </div>

            <div class="content-section">
                <div class="section-marker">区域4</div>
                <h3>第四部分：底部测试区域</h3>
                <p class="test-text">
                    这是最后一个测试区域。<span class="highlight">测试在容器最底部选中文字的情况。</span>
                    工具栏应该能够智能地调整位置，确保始终可见。
                </p>
                <p class="test-text">
                    Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates
                    repudiandae sint et molestiae non recusandae. Itaque earum rerum hic tenetur a sapiente delectus.
                </p>
                <p class="test-text">
                    Ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.
                    Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat.
                </p>
            </div>
        </div>
    </div>

    <div class="mock-selection-bar" id="mockSelectionBar">
        📝 模拟划词工具栏
    </div>

    <script>
        const mockBar = document.getElementById('mockSelectionBar');
        const scrollIndicator = document.getElementById('scrollIndicator');
        const containerScrollYSpan = document.getElementById('containerScrollY');
        const pageScrollYSpan = document.getElementById('pageScrollY');
        const scrollSourceSpan = document.getElementById('scrollSource');
        const scrollCountSpan = document.getElementById('scrollCount');
        const scrollableContent = document.getElementById('scrollableContent');

        let scrollEventCount = 0;

        // 更新滚动指示器
        function updateScrollIndicator(source = '未知') {
            const containerScrollY = scrollableContent.scrollTop;
            const pageScrollY = window.pageYOffset || document.documentElement.scrollTop;

            containerScrollYSpan.textContent = Math.round(containerScrollY);
            pageScrollYSpan.textContent = Math.round(pageScrollY);
            scrollSourceSpan.textContent = source;
            scrollCountSpan.textContent = ++scrollEventCount;
        }

        // 模拟划词工具栏定位逻辑（针对内部滚动容器优化）
        function positionMockSelectionBar() {
            const selection = window.getSelection();
            if (!selection || selection.rangeCount === 0) {
                mockBar.style.display = 'none';
                return;
            }

            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();

            // 如果选择区域不可见，隐藏工具栏
            if (rect.width === 0 && rect.height === 0) {
                mockBar.style.display = 'none';
                return;
            }

            const offsetY = 8;
            const barWidth = 200;
            const barHeight = 40;

            // 获取页面滚动偏移量（对于内部滚动容器，页面滚动通常为0）
            const pageScrollX = window.pageXOffset || document.documentElement.scrollLeft;
            const pageScrollY = window.pageYOffset || document.documentElement.scrollTop;

            // 获取内部容器的滚动偏移量
            const containerScrollX = scrollableContent.scrollLeft;
            const containerScrollY = scrollableContent.scrollTop;

            // 将视口坐标转换为文档坐标
            // 对于内部滚动容器，我们需要考虑容器的滚动位置
            let left = rect.left + pageScrollX;
            let top = rect.bottom + pageScrollY + offsetY;

            // 获取视口尺寸
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // 确保工具栏不会超出视口右边界
            if (left + barWidth > viewportWidth) {
                left = rect.right + pageScrollX - barWidth;
            }

            // 确保工具栏不会超出视口下边界
            if (top + barHeight > viewportHeight) {
                top = rect.top + pageScrollY - barHeight - offsetY;
            }

            // 确保不会超出左边界和上边界
            left = Math.max(5, left);
            top = Math.max(5, top);

            mockBar.style.left = `${left}px`;
            mockBar.style.top = `${top}px`;
            mockBar.style.display = 'block';

            console.log('Mock bar positioned at:', {
                left,
                top,
                pageScrollX,
                pageScrollY,
                containerScrollX,
                containerScrollY,
                rect
            });
        }

        // 处理文本选择
        function handleSelection() {
            setTimeout(() => {
                const selection = window.getSelection();
                if (selection && selection.toString().trim()) {
                    positionMockSelectionBar();
                } else {
                    mockBar.style.display = 'none';
                }
            }, 10);
        }

        // 处理内部容器滚动事件
        function handleContainerScroll(e) {
            console.log('内部容器滚动事件触发', e.target);
            updateScrollIndicator('内部容器');
            // 如果有选中的文本，重新定位工具栏
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                positionMockSelectionBar();
            }
        }

        // 处理页面滚动事件（应该很少触发，因为body滚动被禁用了）
        function handlePageScroll(e) {
            console.log('页面滚动事件触发', e.target);
            updateScrollIndicator('页面');
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                positionMockSelectionBar();
            }
        }

        // 绑定事件监听器
        document.addEventListener('mouseup', handleSelection);
        document.addEventListener('keyup', handleSelection);
        document.addEventListener('selectionchange', handleSelection);

        // 监听内部容器的滚动事件
        scrollableContent.addEventListener('scroll', handleContainerScroll, { passive: true });

        // 同时监听页面滚动事件（用于对比）
        window.addEventListener('scroll', handlePageScroll, { passive: true });
        document.addEventListener('scroll', handlePageScroll, { passive: true });

        window.addEventListener('resize', () => {
            updateScrollIndicator('窗口调整');
            positionMockSelectionBar();
        });

        // 初始化
        updateScrollIndicator('初始化');
    </script>
</body>
</html>
