<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动行为测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            width: 500px;
            border: 1px solid #E5E7EB;
            padding: 16px;
            margin: 0 auto;
        }
        
        .content-area {
            margin-top: 12px;
            max-height: 300px;
            overflow: auto;
            border: 1px solid #ddd;
            padding: 10px;
            
            /* 自定义滚动条样式 */
            &::-webkit-scrollbar {
                width: 2px;
            }
            
            &::-webkit-scrollbar-track {
                background: transparent;
            }
            
            &::-webkit-scrollbar-thumb {
                background: #D1D5DB;
                border-radius: 1px;
            }
            
            &::-webkit-scrollbar-thumb:hover {
                background: #9CA3AF;
            }
            
            /* Firefox 滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: #D1D5DB transparent;
        }
        
        .content-area::-webkit-scrollbar {
            width: 2px;
        }
        
        .content-area::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .content-area::-webkit-scrollbar-thumb {
            background: #D1D5DB;
            border-radius: 1px;
        }
        
        .content-area::-webkit-scrollbar-thumb:hover {
            background: #9CA3AF;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #4285F4;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #3367D6;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #e8f4fd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .cursor {
            animation: blink 1s infinite;
            color: #4096FF;
            font-weight: bold;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>AI消息滚动行为测试</h2>
        
        <div class="controls">
            <button onclick="startSimulation()">开始流式消息模拟</button>
            <button onclick="stopSimulation()">停止模拟</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>
        
        <div class="content-area" id="contentArea">
            <div class="message">欢迎使用AI助手！这是一条初始消息。</div>
        </div>
        
        <div class="status" id="status">
            状态：准备就绪<br>
            用户滚动状态：<span id="userScrolling">否</span><br>
            是否在底部：<span id="atBottom">是</span>
        </div>
    </div>

    <script>
        let isSimulating = false;
        let messageCount = 1;
        let isUserScrolling = false;
        let scrollTimeout = null;
        
        const contentArea = document.getElementById('contentArea');
        const statusEl = document.getElementById('status');
        const userScrollingEl = document.getElementById('userScrolling');
        const atBottomEl = document.getElementById('atBottom');
        
        // 检查是否滚动到底部
        function isScrolledToBottom() {
            const { scrollTop, scrollHeight, clientHeight } = contentArea;
            return Math.abs(scrollTop + clientHeight - scrollHeight) <= 3;
        }
        
        // 滚动到底部
        function scrollToBottom(smooth = true) {
            contentArea.scrollTo({
                top: contentArea.scrollHeight,
                behavior: smooth ? 'smooth' : 'auto'
            });
        }
        
        // 处理滚动事件
        function handleScroll() {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            
            const atBottom = isScrolledToBottom();
            
            if (!atBottom) {
                isUserScrolling = true;
                scrollTimeout = setTimeout(() => {
                    if (isScrolledToBottom()) {
                        isUserScrolling = false;
                    }
                }, 500);
            } else {
                isUserScrolling = false;
            }
            
            updateStatus();
        }
        
        // 更新状态显示
        function updateStatus() {
            userScrollingEl.textContent = isUserScrolling ? '是' : '否';
            atBottomEl.textContent = isScrolledToBottom() ? '是' : '否';
        }
        
        // 添加消息
        function addMessage(content, isStreaming = false) {
            const wasAtBottom = isScrolledToBottom();
            
            const messageEl = document.createElement('div');
            messageEl.className = 'message';
            messageEl.innerHTML = content + (isStreaming ? '<span class="cursor">|</span>' : '');
            
            contentArea.appendChild(messageEl);
            
            // 智能滚动逻辑
            requestAnimationFrame(() => {
                if (wasAtBottom && !isUserScrolling) {
                    scrollToBottom(true);
                }
            });
            
            updateStatus();
        }
        
        // 开始模拟
        function startSimulation() {
            if (isSimulating) return;
            
            isSimulating = true;
            statusEl.innerHTML = '状态：正在模拟流式消息...<br>用户滚动状态：<span id="userScrolling">否</span><br>是否在底部：<span id="atBottom">是</span>';
            
            const messages = [
                "正在分析您的请求...",
                "这是一条较长的消息，用来测试滚动行为。当消息内容很多时，用户应该能够自由地向上滚动查看历史消息。",
                "同时，当有新消息到达时，如果用户在底部，应该自动滚动到最新消息。",
                "但是，如果用户正在查看历史消息（不在底部），则不应该强制滚动到底部。",
                "这样可以确保良好的用户体验。",
                "让我们继续添加更多消息来测试这个功能...",
                "消息 7: 测试内容",
                "消息 8: 更多测试内容",
                "消息 9: 继续测试滚动行为",
                "消息 10: 最后一条测试消息"
            ];
            
            let index = 0;
            const interval = setInterval(() => {
                if (index < messages.length && isSimulating) {
                    addMessage(`消息 ${messageCount++}: ${messages[index]}`, index < messages.length - 1);
                    index++;
                } else {
                    clearInterval(interval);
                    isSimulating = false;
                    statusEl.innerHTML = '状态：模拟完成<br>用户滚动状态：<span id="userScrolling">否</span><br>是否在底部：<span id="atBottom">是</span>';
                    updateStatus();
                }
            }, 1000);
        }
        
        // 停止模拟
        function stopSimulation() {
            isSimulating = false;
            statusEl.innerHTML = '状态：已停止<br>用户滚动状态：<span id="userScrolling">否</span><br>是否在底部：<span id="atBottom">是</span>';
            updateStatus();
        }
        
        // 清空消息
        function clearMessages() {
            contentArea.innerHTML = '<div class="message">欢迎使用AI助手！这是一条初始消息。</div>';
            messageCount = 1;
            isUserScrolling = false;
            updateStatus();
        }
        
        // 绑定滚动事件
        contentArea.addEventListener('scroll', handleScroll);
        
        // 初始化状态
        updateStatus();
    </script>
</body>
</html>
