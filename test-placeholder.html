<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>占位符机制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .test-case h3 {
            margin-top: 0;
            color: #333;
        }

        .original,
        .placeholder,
        .translated,
        .final {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }

        .original {
            background-color: #f0f8ff;
        }

        .placeholder {
            background-color: #fff8dc;
        }

        .translated {
            background-color: #f0fff0;
        }

        .final {
            background-color: #ffe4e1;
        }

        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: 'Courier New', monospace;
        }

        strong {
            color: #d63384;
        }

        .result {
            font-weight: bold;
        }
    </style>
</head>

<body>
    <h1>占位符机制测试</h1>
    <p>这个页面用于测试HTML标签占位符机制，模拟翻译过程中保持HTML结构的功能。</p>

    <div id="test-results">
        <p class="schema-faq-answer"> To get the <strong>best results</strong> " from AI-generated prompts, follow these
            " <strong>best practices</strong> ". <br> <img draggable="false" role="img" class="emoji" alt="✔"
                src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg">
            <strong>Test and refine</strong> " — Run the prompt and tweak if
            necessary." <br> <img draggable="false" role="img" class="emoji" alt="✔"
                src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg"> <strong>Use
                examples</strong> " — If possible, provide a sample format." <br> <img draggable="false" role="img"
                class="emoji" alt="✔" src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg">
            <strong>Be concise yet detailed</strong> " — Provide enough details
            but avoid unnecessary complexity." <br> <img draggable="false" role="img" class="emoji" alt="✔"
                src="https://s.w.org/image/core/emoji/15.0.3/svg/2714.svg"> <strong>Specify tone &
                format</strong> " — (e.g., "Write in a professional tone, max 200 words")." <br> Our AI Prompt Generator
            <strong>ensures</strong> " these best practices are followed
            automatically."
        </p>
    </div>

    <script>
        // 占位符映射接口
        const SEMANTIC_TAGS = [
            'strong', 'b', 'em', 'i', 'span', 'a', 'code', 'mark',
            'u', 's', 'sub', 'sup', 'small', 'big', 'cite', 'q',
            'abbr', 'dfn', 'time', 'var', 'samp', 'kbd'
        ];

        const blankNodeList = [
            'SCRIPT', 'STYLE', 'LINK', 'SVG', 'KBD', 'PRE', 'IMG', 'PATH',
            'VIDEO', 'AUDIO', 'SOURCE', 'CANVAS', 'IFRAME', 'CODE', 'FOOTER', 'NAV'
        ];

        /**
         * 将HTML内容转换为带占位符的文本
         */
        function convertHtmlToPlaceholders(htmlContent) {
            const placeholderMap = {};
            let placeholderCounter = 0;

            // 创建临时容器来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // 递归处理节点，将语义化标签替换为占位符
            function processNode(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    return node.textContent || '';
                }

                if (node.nodeType === Node.ELEMENT_NODE) {
                    const element = node;
                    const tagName = element.tagName.toLowerCase();

                    // 跳过图片等不需要翻译的元素
                    if (blankNodeList.includes(element.tagName)) {
                        return '';
                    }

                    // 如果是语义化标签，创建占位符
                    if (SEMANTIC_TAGS.includes(tagName)) {
                        const placeholderKey = `{${tagName}${placeholderCounter}}`;
                        const closingKey = `{/${tagName}${placeholderCounter}}`;
                        placeholderCounter++;

                        // 获取元素属性
                        const attributes = {};
                        for (let i = 0; i < element.attributes.length; i++) {
                            const attr = element.attributes[i];
                            attributes[attr.name] = attr.value;
                        }

                        // 递归处理子节点获取内容
                        const content = Array.from(element.childNodes)
                            .map(child => processNode(child))
                            .join('');

                        // 存储占位符映射
                        placeholderMap[placeholderKey] = {
                            tagName,
                            attributes,
                            content
                        };

                        return `${placeholderKey}${content}${closingKey}`;
                    } else {
                        // 对于其他元素，直接处理子节点
                        return Array.from(element.childNodes)
                            .map(child => processNode(child))
                            .join('');
                    }
                }

                return '';
            }

            const placeholderText = Array.from(tempDiv.childNodes)
                .map(node => processNode(node))
                .join('')
                .replace(/\s+/g, ' ')
                .trim();

            return { placeholderText, placeholderMap };
        }

        /**
         * 将带占位符的翻译文本转换回HTML结构
         */
        function convertPlaceholdersToHtml(translatedText, placeholderMap) {
            let result = translatedText;

            // 按占位符出现的顺序处理，确保嵌套结构正确
            const placeholderKeys = Object.keys(placeholderMap);

            // 处理每个占位符
            placeholderKeys.forEach(placeholderKey => {
                const mapping = placeholderMap[placeholderKey];
                const { tagName, attributes } = mapping;

                // 构建开始标签
                let startTag = `<${tagName}`;
                Object.entries(attributes).forEach(([name, value]) => {
                    startTag += ` ${name}="${value}"`;
                });
                startTag += '>';

                // 构建结束标签
                const endTag = `</${tagName}>`;
                const closingKey = `{/${tagName}${placeholderKey.match(/\d+/)?.[0] || '0'}}`;

                // 替换占位符对
                const placeholderPattern = new RegExp(
                    `\\${placeholderKey}(.*?)\\${closingKey}`,
                    'g'
                );

                result = result.replace(placeholderPattern, (match, innerContent) => {
                    return `${startTag}${innerContent}${endTag}`;
                });
            });

            return result;
        }

        // 测试用例
        const testCases = [
            {
                name: '简单的strong标签',
                original: 'To get the <strong>best results</strong> from AI-generated prompts',
                translated: '要从AI生成的提示中获得{strong0}最佳结果{/strong0}'
            },
            {
                name: '嵌套的span和strong标签',
                original: '<span>普通文本<strong>重要文本</strong>更多文本</span>',
                translated: '{span0}Normal text{strong1}Important text{/strong1}More text{/span0}'
            },
            {
                name: '带属性的strong标签',
                original: 'To get the <strong >best results</strong> from AI-generated prompts, follow these <strong >best practices</strong>.',
                translated: 'To get the {strong0}best results{/strong0} from AI-generated prompts, follow these {strong1}best practices{/strong1}.'
            },
            {
                name: '复杂的多标签结构',
                original: '<strong>Test and refine</strong> — Run the prompt and tweak if necessary.',
                translated: '{strong0}测试和完善{/strong0} — 运行提示并根据需要进行调整。'
            }
        ];

        // 运行测试
        function runTests() {
            const resultsContainer = document.getElementById('test-results');

            // 处理原始HTML
            const result = convertHtmlToPlaceholders(testCase.original);
            const finalHtml = convertPlaceholdersToHtml(testCase.translated, result.placeholderMap);

            testDiv.innerHTML = `
                    <h3>测试用例 ${index + 1}: ${testCase.name}</h3>
                    <div class="original">
                        <strong>原始HTML:</strong><br>
                        <code>${escapeHtml(testCase.original)}</code><br>
                        <div class="result">渲染结果: ${testCase.original}</div>
                    </div>
                    <div class="placeholder">
                        <strong>占位符文本:</strong><br>
                        <code>${escapeHtml(result.placeholderText)}</code><br>
                        <strong>占位符映射:</strong><br>
                        <code>${escapeHtml(JSON.stringify(result.placeholderMap, null, 2))}</code>
                    </div>
                    <div class="translated">
                        <strong>翻译后的占位符文本:</strong><br>
                        <code>${escapeHtml(testCase.translated)}</code>
                    </div>
                    <div class="final">
                        <strong>最终HTML:</strong><br>
                        <code>${escapeHtml(finalHtml)}</code><br>
                        <div class="result">渲染结果: ${finalHtml}</div>
                    </div>
                `;

            resultsContainer.appendChild(testDiv);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>

</html>