{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[less]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}}