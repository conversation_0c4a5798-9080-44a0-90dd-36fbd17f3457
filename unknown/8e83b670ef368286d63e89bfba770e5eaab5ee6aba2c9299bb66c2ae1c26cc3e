/**
 * Actions 管理
 * 简化的action管理系统，只维护翻译相关的actions
 */

// 导入翻译actions
import { getTranslationActions } from './translationActions'

// Action基础类型定义
export interface BaseAction {
  name: string;
  handler: (...args: any[]) => void;
  description?: string;
}

/**
 * 创建划词工具栏数据更新action
 * 用于处理和拼接流式消息数据
 */
export const createUpdateModalDataAction = (): BaseAction => {
  return {
    name: 'updateModalData',
    description: '更新和拼接处理流式消息数据，用于划词工具栏显示',
    handler: async (response: any) => {
      // 处理流式数据更新逻辑
      if (response && response.list && Array.isArray(response.list)) {
        // 提取流式消息内容
        const streamTexts = response.list
          .filter((item: any) => item.content && item.content.text)
          .map((item: any) => item.content.text);

        // 拼接流式内容
        let concatenatedText = streamTexts.join('');

        if (concatenatedText) {
          // 更新模态框数据
          // 提取 conversationId（如果存在）
          const conversationId = response.conversationId || null;
          const agentId = response.agentId || null;

          //如果agentId === "translate",尝试将concatenatedText parse后，如果是数组，那么取数组的第一项
          if (agentId === 'translate') {
            try {
              const textArray = JSON.parse(concatenatedText);
              if (Array.isArray(textArray)) {
                concatenatedText = textArray[0];
              }
            } catch (error) {
              // JSON解析失败，说明不是数组，直接使用原始文本
            }
          }
          // 通过 webAssistantManager 更新 AIProcessModal 数据
          if (window.webAssistantManager) {
            window.webAssistantManager.updateAIProcessModalData(
              concatenatedText,
              response.endFlag || false,
              conversationId
            );
          }
        }
      }
    }
  };
};

/**
 * 获取所有可用的actions
 */
export const getAllActions = (): BaseAction[] => {
  return [
    ...getTranslationActions(),
    createUpdateModalDataAction()
  ]
}

// 默认导出所有actions（用于ChatUI）
export default getAllActions
