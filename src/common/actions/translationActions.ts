/**
 * 翻译相关的Action函数
 */

import type { BaseAction } from './index'
import { MessageType } from '../const';
import { getCurrentTab } from '@src/sidepanel/utils';
import { getCurrentEnv } from '../utils';
import { handleInsertDom } from '@src/contents/scripts/injectTranslate';

/**
 * 处理翻译API返回值的action函数
 * 这个函数封装了原本在批量翻译处理中的响应处理逻辑
 */
export const createInsertDomAction = (): BaseAction => {
  return {
    name: 'insertDom',
    description: '处理翻译API返回值，更新页面翻译图标',
    handler: async (response: any, group: any[]) => {
      console.log('handler', response);
      if (response.endFlag) {
        // 处理 response.list 数据结构
        if (response.list && Array.isArray(response.list)) {
          // 提取并解析每个对象的 content.text 字段
          const results: string[] = [];
          let useNewFormat = false;

          for (const item of response.list) {
            if (item.content && item.content.text) {
              try {
                // 尝试解析JSON字符串为数组（新格式）
                const textArray = JSON.parse(item.content.text);
                if (Array.isArray(textArray)) {
                  results.push(...textArray);
                  useNewFormat = true;
                } else {
                  // 如果解析成功但不是数组，直接添加
                  results.push(item.content.text);
                  useNewFormat = true;
                }
              } catch (error) {
                // JSON解析失败，说明是旧格式，跳出循环使用旧逻辑
                console.log('检测到旧格式数据，使用兼容模式处理');
                break;
              }
            }
          }

          if (useNewFormat && results.length > 0) {
            // 使用新格式处理结果
            console.log('使用新格式提取的翻译结果数组:', results, getCurrentEnv());
            //侧栏环境中
            // if (getCurrentEnv() === 'sidepanel') {
            //   const currentTab = await getCurrentTab()
            //   chrome.tabs.sendMessage(currentTab.id,
            //     {
            //       type: MessageType.INSERT_DOM,
            //       data: results,
            //     }
            //   )
            //   return
            // }
            handleInsertDom(results)
          } else {
            // 兼容旧格式：拼接所有text内容然后用###分割
            const concatenatedText = response.list
              .filter((item: any) => item.content && item.content.text)
              .map((item: any) => item.content.text)
              .join('');

            if (concatenatedText) {
              const results = concatenatedText.split('###');
              console.log('使用旧格式分割的翻译结果:', results, getCurrentEnv());
              handleInsertDom(results)
            } else {
              console.error('翻译失败: 未能从响应中提取到有效的翻译结果', response);
            }
          }
        } else {
          // 处理错误情况
          console.error('翻译失败: 响应格式不正确或缺少必要数据', response?.result || '未知错误');
        }
      }


    }
  };
};

/**
 * 获取所有翻译相关的actions
 */
export const getTranslationActions = (): BaseAction[] => {
  return [
    createInsertDomAction()
  ];
};