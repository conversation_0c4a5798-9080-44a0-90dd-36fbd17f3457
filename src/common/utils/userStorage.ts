/**
 * 用户信息存储工具
 * 使用 Chrome Storage API 进行数据持久化
 */

export interface UserInfo {
  // 根据实际数据结构定义用户信息接口
  id?: string
  userId?: string  // 添加userId字段，与settings.tsx中使用的字段保持一致
  name?: string
  userName?: string  // 添加userName字段，与settings.tsx中使用的字段保持一致
  email?: string
  token?: string
  avatar?: string
  // 可以添加更多用户信息字段
  [key: string]: any
}

export const userStorage = {
  /**
   * 设置用户信息
   * @param userInfo 用户信息对象
   */
  async setUserInfo(userInfo: UserInfo): Promise<void> {
    try {
      await chrome.storage.local.set({ userInfo })
      console.log('用户信息已保存到本地存储')
    } catch (error) {
      console.error('保存用户信息失败:', error)
      throw error
    }
  },

  /**
   * 获取用户信息
   * @returns 用户信息对象，如果不存在则返回 null
   */
  async getUserInfo(): Promise<UserInfo | null> {
    try {
      const result = await chrome.storage.local.get(['userInfo'])
      return result.userInfo || null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  },

  /**
   * 清除用户信息
   */
  async clearUserInfo(): Promise<void> {
    try {
      await chrome.storage.local.remove(['userInfo'])
      console.log('用户信息已清除')
    } catch (error) {
      console.error('清除用户信息失败:', error)
      throw error
    }
  },

  /**
   * 检查用户是否已登录
   * @returns 是否已登录
   */
  async isLoggedIn(): Promise<boolean> {
    try {
      const userInfo = await this.getUserInfo()
      return userInfo !== null && Object.keys(userInfo).length > 0
    } catch (error) {
      console.error('检查登录状态失败:', error)
      return false
    }
  },

  /**
   * 更新部分用户信息
   * @param updates 要更新的用户信息字段
   */
  async updateUserInfo(updates: Partial<UserInfo>): Promise<void> {
    try {
      const currentUserInfo = await this.getUserInfo()
      if (currentUserInfo) {
        const updatedUserInfo = { ...currentUserInfo, ...updates }
        await this.setUserInfo(updatedUserInfo)
        console.log('用户信息已更新')
      } else {
        throw new Error('没有找到用户信息，无法更新')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }
}

// 导出类型
export type { UserInfo }
