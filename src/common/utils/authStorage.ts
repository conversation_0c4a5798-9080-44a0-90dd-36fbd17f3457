/**
 * 认证信息存储工具
 * 用于存储各种认证相关的信息（不包括wiki认证，wiki认证直接从浏览器cookie获取）
 */

export interface AuthInfo {
    // 可以添加各种认证信息，但不包括wiki
    [key: string]: any
}

export const authStorage = {
    /**
     * 设置认证信息
     * @param authInfo 认证信息对象
     */
    async setAuthInfo(authInfo: AuthInfo): Promise<void> {
        try {
            await chrome.storage.local.set({ authInfo })
            console.log('认证信息已保存到本地存储:', authInfo)
        } catch (error) {
            console.error('保存认证信息失败:', error)
            throw error
        }
    },

    /**
     * 获取认证信息
     * @returns 认证信息对象，如果不存在则返回 null
     */
    async getAuthInfo(): Promise<AuthInfo | null> {
        try {
            const result = await chrome.storage.local.get(['authInfo'])
            return result.authInfo || null
        } catch (error) {
            console.error('获取认证信息失败:', error)
            return null
        }
    },

    /**
     * 更新部分认证信息
     * @param updates 要更新的认证信息字段
     */
    async updateAuthInfo(updates: Partial<AuthInfo>): Promise<void> {
        try {
            const currentAuthInfo = await this.getAuthInfo() || {}
            const updatedAuthInfo = { ...currentAuthInfo, ...updates }

            // 如果某个字段被设置为undefined，则删除该字段
            Object.keys(updatedAuthInfo).forEach(key => {
                if (updatedAuthInfo[key] === undefined) {
                    delete updatedAuthInfo[key]
                }
            })

            await this.setAuthInfo(updatedAuthInfo)
        } catch (error) {
            console.error('更新认证信息失败:', error)
            throw error
        }
    },

    /**
     * 清除所有认证信息
     */
    async clearAuthInfo(): Promise<void> {
        try {
            await chrome.storage.local.remove(['authInfo'])
            console.log('认证信息已清除')
        } catch (error) {
            console.error('清除认证信息失败:', error)
            throw error
        }
    }
}
