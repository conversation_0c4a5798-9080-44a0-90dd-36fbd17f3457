/**
 * 用户配置存储工具函数
 * 使用API接口进行数据获取和保存
 */

import {
  getUserAssistantConfig,
  saveUserAssistantConfig,
  getUserId,
  type UserAssistantConfig
} from './userConfigApi'

// 存储键名常量
export const STORAGE_KEYS = {
  FLOATING_DISABLED_WEBSITES: 'floatingDisabledWebsites',
  SELECTION_DISABLED_WEBSITES: 'selectionDisabledWebsites',
  TRANSLATION_DEFAULT_TARGET_LANGUAGE: 'translationDefaultTargetLanguage',
} as const

// 存储数据类型定义
export interface StorageData {
  [STORAGE_KEYS.FLOATING_DISABLED_WEBSITES]: Array<{
    id: string
    domain: string
  }>
  [STORAGE_KEYS.SELECTION_DISABLED_WEBSITES]: Array<{
    id: string
    domain: string
  }>
  [STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE]: string
}

/**
 * 获取存储数据
 * @param keys 要获取的键名数组
 * @returns Promise<Partial<StorageData>>
 */
export const getStorageData = async <K extends keyof StorageData>(
  keys: K[]
): Promise<Pick<StorageData, K>> => {
  try {
    const userId = await getUserId()
    const response = await getUserAssistantConfig({ userId })

    // 确保响应是成功的
    if (!response || typeof response !== 'object' || 'code' in response === false) {
      throw new Error('无效的API响应')
    }

    if (response.code !== '0') {
      throw new Error(response.msg || '获取配置失败')
    }

    const config = response.resultData
    const result: Partial<StorageData> = {}

    // 转换API返回的数据格式为本地存储格式
    if (keys.includes(STORAGE_KEYS.FLOATING_DISABLED_WEBSITES as K)) {
      result[STORAGE_KEYS.FLOATING_DISABLED_WEBSITES] = config.floatingDisabledWebsites?.map(
        (domain, index) => ({ id: `floating_${index}`, domain })
      )
    }

    if (keys.includes(STORAGE_KEYS.SELECTION_DISABLED_WEBSITES as K)) {
      result[STORAGE_KEYS.SELECTION_DISABLED_WEBSITES] = config.selectionDisabledWebsites?.map(
        (domain, index) => ({ id: `selection_${index}`, domain })
      )
    }

    if (keys.includes(STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE as K)) {
      result[STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE] = config.translationDefaultTargetLanguage
    }

    return result as Pick<StorageData, K>
  } catch (error) {
    console.error('获取存储数据失败:', error)
    // 如果API调用失败，返回默认值
    const defaultValues: Partial<StorageData> = {}

    if (keys.includes(STORAGE_KEYS.FLOATING_DISABLED_WEBSITES as K)) {
      defaultValues[STORAGE_KEYS.FLOATING_DISABLED_WEBSITES] = []
    }

    if (keys.includes(STORAGE_KEYS.SELECTION_DISABLED_WEBSITES as K)) {
      defaultValues[STORAGE_KEYS.SELECTION_DISABLED_WEBSITES] = []
    }

    if (keys.includes(STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE as K)) {
      defaultValues[STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE] = 'English (英语)'
    }

    return defaultValues as Pick<StorageData, K>
  }
}

/**
 * 设置存储数据
 * @param data 要存储的数据对象
 * @returns Promise<void>
 */
export const setStorageData = async (
  data: Partial<StorageData>
): Promise<void> => {
  try {
    const userId = await getUserId()

    // 转换本地存储格式为API请求格式
    const requestData: any = { userId }

    if (data[STORAGE_KEYS.FLOATING_DISABLED_WEBSITES]) {
      requestData.floatingDisabledWebsites = data[STORAGE_KEYS.FLOATING_DISABLED_WEBSITES]?.map(
        item => item.domain
      ) || []
    }

    if (data[STORAGE_KEYS.SELECTION_DISABLED_WEBSITES]) {
      requestData.selectionDisabledWebsites = data[STORAGE_KEYS.SELECTION_DISABLED_WEBSITES]?.map(
        item => item.domain
      ) || []
    }

    if (data[STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE]) {
      requestData.translationDefaultTargetLanguage = data[STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE]
    }

    const response = await saveUserAssistantConfig(requestData)

    // 确保响应是成功的
    if (!response || typeof response !== 'object' || 'code' in response === false) {
      throw new Error('无效的API响应')
    }

    if (response.code !== '0') {
      throw new Error(response.msg || '保存配置失败')
    }

    console.log('配置保存成功')
  } catch (error) {
    console.error('设置存储数据失败:', error)
    throw error
  }
}




/**
 * 获取所有配置信息
 * @returns Promise<StorageData>
 */
export const getAllConfig = async (): Promise<StorageData> => {
  try {
    const allKeys = Object.values(STORAGE_KEYS)
    return await getStorageData(allKeys)
  } catch (error) {
    console.error('获取所有配置失败:', error)
    throw error
  }
}


