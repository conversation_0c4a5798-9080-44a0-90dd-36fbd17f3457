import { useState, useEffect } from 'react'
import { buildApi } from './api'
import { getBaseUrl } from './baseUrl'

export interface ApiResponse<T> {
  code: string
  msg: string
  resultData: T
}

export interface GetLastVersionRequest {
  version: string
}

export interface GetLastVersionResponse {
  isLatestVersion: boolean
  latestVersion: string
}

export interface UseVersionReturn {
  isLatestVersion: boolean | null
  latestVersion: string | null
}

/**
 * 获取当前插件版本
 */
export const getCurrentVersion = (): string => {
  return chrome.runtime.getManifest().version
}

/**
 * 对比并获取最新插件版本号
 */
export const getLastVersion = buildApi<
  GetLastVersionRequest,
  ApiResponse<GetLastVersionResponse>
>('POST', '/web/assistant/version/getLastVersion', {
  baseUrl: getBaseUrl(),
  transform: (res) => res as ApiResponse<GetLastVersionResponse>,
})

/**
 * 版本检查 Hook
 * @returns {UseVersionReturn} 版本检查结果
 */
export const useVersion = (): UseVersionReturn => {
  const [isLatestVersion, setIsLatestVersion] = useState<boolean | null>(null)
  const [latestVersion, setLatestVersion] = useState<string | null>(null)

  useEffect(() => {
    const checkVersion = async () => {
      try {
        const currentVersion = getCurrentVersion()
        const result = await getLastVersion({ version: currentVersion }) as ApiResponse<GetLastVersionResponse>

        if (result.code === '0' && result.resultData) {
          setIsLatestVersion(result.resultData.isLatestVersion)
          setLatestVersion(result.resultData.latestVersion)
          console.log('currentVersion', currentVersion)
          console.log('latestVersion', result.resultData.latestVersion)

        } else {
          setIsLatestVersion(null)
          setLatestVersion(null)
        }
      } catch (err) {
        console.error('版本检查失败:', err)
        setIsLatestVersion(null)
        setLatestVersion(null)
      }
    }

    checkVersion()
  }, [])

  return {
    isLatestVersion,
    latestVersion
  }
}

export default useVersion
