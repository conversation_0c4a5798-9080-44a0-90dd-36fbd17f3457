export * from './api'
export * from './env'
export * from './storage'
export { default as ipConnectivity } from './ipConnectivity'
export * from './userStorage'
export * from './authStorage'
export * from './wikiAuth'

export const isExtensionPage = (url: string) => {
  const urlPrefixes = [
    'chrome://',
    'chrome-extension://',
    'edge://',
    'about:',
    'https://chrome.google.com/webstore',
    'https://microsoftedge.microsoft.com/addons',
    'https://addons.mozilla.org/en-US/firefox',
    'https://chromewebstore.google.com/',
  ]
  return urlPrefixes.some((urlPrefix) => url.startsWith(urlPrefix))
}

// 环境检测函数
export function getCurrentEnv() {
  try {
    // 扩展环境可以访问 runtime.id，content script 不能
    if (chrome.runtime && chrome.runtime.id) {
      return "sidepanel"; // 侧栏/扩展环境
    } else {
      return "content_script"; // content script 环境
    }
  } catch (e) {
    return "content_script"; // 访问失败时判定为 content script
  }
}