import React from 'react'

interface ImgIconProps {
  src: string
  alt?: string
  className?: string
  width?: number | string
  height?: number | string
  onClick?: () => void
}

export const ImgIcon: React.FC<ImgIconProps> = ({
  src,
  alt = '',
  className = '',
  width = '16',
  height = '16',
  onClick,
}) => {
  return (
    <img
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      style={{ objectFit: 'contain' }}
      onClick={onClick}
    />
  )
}

export default ImgIcon
