/**
 * 测试占位符机制的功能
 */

// 占位符映射接口
interface PlaceholderMap {
  [key: string]: {
    tagName: string
    attributes: Record<string, string>
    content: string
  }
}

// 语义化HTML标签列表（需要保持结构的标签）
const SEMANTIC_TAGS = [
  'strong', 'b', 'em', 'i', 'span', 'a', 'code', 'mark', 
  'u', 's', 'sub', 'sup', 'small', 'big', 'cite', 'q', 
  'abbr', 'dfn', 'time', 'var', 'samp', 'kbd'
]

const blankNodeList = [
  'SCRIPT', 'STYLE', 'LINK', 'SVG', 'KBD', 'PRE', 'IMG', 'PATH',
  'VIDEO', 'AUDIO', 'SOURCE', 'CANVAS', 'IFRAME', 'CODE', 'FOOTER', 'NAV'
]

/**
 * 将HTML内容转换为带占位符的文本
 */
function convertHtmlToPlaceholders(htmlContent: string): {
  placeholderText: string
  placeholderMap: PlaceholderMap
} {
  const placeholderMap: PlaceholderMap = {}
  let placeholderCounter = 0
  
  // 创建临时容器来解析HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = htmlContent
  
  // 递归处理节点，将语义化标签替换为占位符
  function processNode(node: Node): string {
    if (node.nodeType === Node.TEXT_NODE) {
      return node.textContent || ''
    }
    
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement
      const tagName = element.tagName.toLowerCase()
      
      // 跳过图片等不需要翻译的元素
      if (blankNodeList.includes(element.tagName)) {
        return ''
      }
      
      // 如果是语义化标签，创建占位符
      if (SEMANTIC_TAGS.includes(tagName)) {
        const placeholderKey = `{${tagName}${placeholderCounter}}`
        const closingKey = `{/${tagName}${placeholderCounter}}`
        placeholderCounter++
        
        // 获取元素属性
        const attributes: Record<string, string> = {}
        for (let i = 0; i < element.attributes.length; i++) {
          const attr = element.attributes[i]
          attributes[attr.name] = attr.value
        }
        
        // 递归处理子节点获取内容
        const content = Array.from(element.childNodes)
          .map(child => processNode(child))
          .join('')
        
        // 存储占位符映射
        placeholderMap[placeholderKey] = {
          tagName,
          attributes,
          content
        }
        
        return `${placeholderKey}${content}${closingKey}`
      } else {
        // 对于其他元素，直接处理子节点
        return Array.from(element.childNodes)
          .map(child => processNode(child))
          .join('')
      }
    }
    
    return ''
  }
  
  const placeholderText = Array.from(tempDiv.childNodes)
    .map(node => processNode(node))
    .join('')
    .replace(/\s+/g, ' ')
    .trim()
  
  return { placeholderText, placeholderMap }
}

/**
 * 将带占位符的翻译文本转换回HTML结构
 */
function convertPlaceholdersToHtml(
  translatedText: string, 
  placeholderMap: PlaceholderMap
): string {
  let result = translatedText
  
  // 按占位符出现的顺序处理，确保嵌套结构正确
  const placeholderKeys = Object.keys(placeholderMap)
  
  // 处理每个占位符
  placeholderKeys.forEach(placeholderKey => {
    const mapping = placeholderMap[placeholderKey]
    const { tagName, attributes } = mapping
    
    // 构建开始标签
    let startTag = `<${tagName}`
    Object.entries(attributes).forEach(([name, value]) => {
      startTag += ` ${name}="${value}"`
    })
    startTag += '>'
    
    // 构建结束标签
    const endTag = `</${tagName}>`
    const closingKey = `{/${tagName}${placeholderKey.match(/\d+/)?.[0] || '0'}}`
    
    // 替换占位符对
    const placeholderPattern = new RegExp(
      `\\${placeholderKey}(.*?)\\${closingKey}`, 
      'g'
    )
    
    result = result.replace(placeholderPattern, (_match, innerContent) => {
      return `${startTag}${innerContent}${endTag}`
    })
  })
  
  return result
}

// 测试函数
function testPlaceholderMechanism() {
  console.log('=== 测试占位符机制 ===')
  
  // 测试用例1：简单的strong标签
  const test1 = 'To get the <strong>best results</strong> from AI-generated prompts'
  console.log('原始HTML:', test1)
  
  const result1 = convertHtmlToPlaceholders(test1)
  console.log('占位符文本:', result1.placeholderText)
  console.log('占位符映射:', result1.placeholderMap)
  
  // 模拟翻译结果
  const translated1 = '要从AI生成的提示中获得{strong0}最佳结果{/strong0}'
  const final1 = convertPlaceholdersToHtml(translated1, result1.placeholderMap)
  console.log('最终HTML:', final1)
  
  console.log('---')
  
  // 测试用例2：复杂的嵌套结构
  const test2 = '<span>普通文本<strong>重要文本</strong>更多文本</span>'
  console.log('原始HTML:', test2)
  
  const result2 = convertHtmlToPlaceholders(test2)
  console.log('占位符文本:', result2.placeholderText)
  console.log('占位符映射:', result2.placeholderMap)
  
  // 模拟翻译结果
  const translated2 = '{span0}Normal text{strong1}Important text{/strong1}More text{/span0}'
  const final2 = convertPlaceholdersToHtml(translated2, result2.placeholderMap)
  console.log('最终HTML:', final2)
  
  console.log('---')
  
  // 测试用例3：您提供的示例
  const test3 = 'To get the <strong data-doubao-translate-traverse-mark="1">best results</strong> from AI-generated prompts, follow these <strong data-doubao-translate-traverse-mark="1">best practices</strong>.'
  console.log('原始HTML:', test3)
  
  const result3 = convertHtmlToPlaceholders(test3)
  console.log('占位符文本:', result3.placeholderText)
  console.log('占位符映射:', result3.placeholderMap)
  
  // 模拟翻译结果
  const translated3 = 'To get the {strong0}best results{/strong0} from AI-generated prompts, follow these {strong1}best practices{/strong1}.'
  const final3 = convertPlaceholdersToHtml(translated3, result3.placeholderMap)
  console.log('最终HTML:', final3)
}

// 如果在浏览器环境中，可以调用测试
if (typeof window !== 'undefined') {
  // 延迟执行，确保DOM加载完成
  setTimeout(testPlaceholderMechanism, 1000)
}

export {
  convertHtmlToPlaceholders,
  convertPlaceholdersToHtml,
  testPlaceholderMechanism
}
