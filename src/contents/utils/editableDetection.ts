/**
 * 可编辑区域检测工具函数
 * 用于检测当前选中的内容是否位于可编辑区域内
 */

/**
 * 检测当前选中的内容是否位于可编辑区域内
 * 通过向上遍历DOM树，检查选中元素或其父元素是否具有contenteditable="true"属性
 *
 * @param cachedRange - 可选的缓存Range对象，如果提供则使用它而不是当前选择
 * @returns {boolean} 如果选中内容在可编辑区域内返回 true，否则返回 false
 */
export const isSelectionInEditableArea = (cachedRange?: Range | null): boolean => {
  try {
    let range: Range | null = null;

    if (cachedRange) {
      // 使用提供的缓存Range
      range = cachedRange;
    } else {
      // 使用当前选择
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        return false;
      }
      range = selection.getRangeAt(0);
    }

    if (!range) {
      return false;
    }

    const container = range.commonAncestorContainer;
    console.log('isSelectionInEditableArea', {
      range,
      container,
      usingCachedRange: !!cachedRange
    });

    // 从选中内容的容器开始向上查找可编辑元素
    let current = container.nodeType === Node.TEXT_NODE
      ? container.parentElement
      : container as Element;

    // 安全检查：确保 current 不为 null
    if (!current) {
      return false;
    }

    // 向上遍历DOM树，直到找到可编辑元素或到达body
    while (current && current !== document.body && current !== document.documentElement) {
      // 检查各种可编辑情况
      if (current instanceof HTMLElement) {
        // 检查 contenteditable 属性
        if (current.isContentEditable) {
          return true;
        }

        // 检查 input 和 textarea 元素
        if (current.tagName === 'INPUT' || current.tagName === 'TEXTAREA') {
          return true;
        }

        // 检查显式设置的 contenteditable 属性
        const contentEditable = current.getAttribute('contenteditable');
        if (contentEditable === 'true' || contentEditable === '') {
          return true;
        }
      }

      current = current.parentElement;
    }

    return false;
  } catch (error) {
    console.warn('Error checking if selection is in editable area:', error);
    return false;
  }
};

/**
 * 检测指定的DOM元素是否为可编辑元素
 * 
 * @param element - 要检测的DOM元素
 * @returns {boolean} 如果元素可编辑返回 true，否则返回 false
 */
export const isElementEditable = (element: Element | null): boolean => {
  if (!element) {
    return false;
  }

  try {
    if (element instanceof HTMLElement) {
      // 检查 contenteditable 属性
      if (element.isContentEditable) {
        return true;
      }

      // 检查 input 和 textarea 元素
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        return true;
      }

      // 检查显式设置的 contenteditable 属性
      const contentEditable = element.getAttribute('contenteditable');
      if (contentEditable === 'true' || contentEditable === '') {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.warn('Error checking if element is editable:', error);
    return false;
  }
};

/**
 * 查找指定元素的可编辑父元素
 * 
 * @param element - 起始元素
 * @returns {HTMLElement | null} 找到的可编辑父元素，如果没有找到返回 null
 */
export const findEditableParent = (element: Element | null): HTMLElement | null => {
  if (!element) {
    return null;
  }

  let current = element instanceof HTMLElement ? element : element.parentElement;

  while (current && current !== document.body && current !== document.documentElement) {
    if (isElementEditable(current)) {
      return current;
    }
    current = current.parentElement;
  }

  return null;
};

/**
 * 检测选中范围是否完全位于可编辑区域内
 * 这个函数会检查选择的起始和结束位置是否都在可编辑区域内
 * 
 * @returns {boolean} 如果整个选择范围都在可编辑区域内返回 true，否则返回 false
 */
export const isSelectionCompletelyInEditableArea = (): boolean => {
  try {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      return false;
    }

    const range = selection.getRangeAt(0);

    // 检查起始位置
    const startContainer = range.startContainer;
    const startElement = startContainer.nodeType === Node.TEXT_NODE
      ? startContainer.parentElement
      : startContainer as Element;

    // 检查结束位置
    const endContainer = range.endContainer;
    const endElement = endContainer.nodeType === Node.TEXT_NODE
      ? endContainer.parentElement
      : endContainer as Element;

    // 查找起始和结束位置的可编辑父元素
    const startEditableParent = findEditableParent(startElement);
    const endEditableParent = findEditableParent(endElement);

    // 只有当起始和结束位置都在可编辑区域内，且是同一个可编辑区域时，才返回 true
    return startEditableParent !== null &&
      endEditableParent !== null &&
      startEditableParent === endEditableParent;
  } catch (error) {
    console.warn('Error checking if selection is completely in editable area:', error);
    return false;
  }
};
