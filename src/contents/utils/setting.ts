import {
    getStorageData,
    STORAGE_KEYS,
} from '@src/common/utils'

type IWebsiteItem = {
    id: string,
    domain: string
}

/**
 * 通过配置获取是否显示悬浮按钮
 * @returns 
 */
export const configShowFloatingButton = async () => {
    const floatSettings = await getStorageData([STORAGE_KEYS.FLOATING_DISABLED_WEBSITES])
    const disabledWebsites: IWebsiteItem[] = floatSettings.floatingDisabledWebsites || [];

    return !disabledWebsites.find(item => item.domain === window.location.hostname)
}