/* 1. 基础容器 */
.modalOverlay {
  position: relative;
  z-index: 10000;
  animation: overlayFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.closing {
    animation: overlayFadeOut 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  width: 500px;
  border: 1px solid #E5E7EB;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.closing {
    animation: modalSlideOut 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* 动画定义 */
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes overlayFadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-12px) scale(0.96);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  to {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
}

/* 2. 头部区域 */
.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  margin-bottom: 12px;
}

.headerLeft {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.aiIcon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.actionTitle {
  color: #333333;
  font-size: 14px;
  font-weight: normal;
  font-weight: 500;
}

.closeButton {
  background: transparent;
  border: none;
  font-size: 16px;
  color: #999999;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;

  &:hover {
    color: #666666;
  }
}

/* 3. 思考过程区域 */
.thinkingSection {
  margin-bottom: 12px;
}

.thinkingToggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  color: #666666;
  font-size: 14px;
  transition: color 0.3s ease;

  &:hover {
    color: #333333;
  }
}

.thinkingText {
  flex: 1;
  text-align: left;
}

.expandIcon {
  color: #666666;
  font-size: 14px;
  transition: all 0.3s ease;

  &.expanded {
    /* 当展开时，显示向上箭头 ∧ */
    transform: rotate(0deg);
  }
}

.thinkingContent {
  padding: 12px 12px 12px 16px;
  background: #F9FAFB;
  border-radius: 6px;
  border-left: 4px solid #D1D5DB;
  margin-top: 8px;
  color: #666666;
  font-size: 12px;
  /* 小两号字体 */
  line-height: 1.6;
  animation: expandContent 0.3s ease-out;

  p {
    margin: 0 0 8px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

@keyframes expandContent {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }

  to {
    opacity: 1;
    max-height: 200px;
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

.errorMessage {
  padding: 12px;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  font-size: 14px;
  color: #e53e3e;
  margin-bottom: 12px;
  display: flex;
  align-items: center;

  &::before {
    content: '⚠️';
    margin-right: 8px;
  }
}

/* 4. 改写结果区域 */
.contentArea {
  margin-top: 12px;
  max-height: 300px;
  overflow: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #D1D5DB;
    border-radius: 1px;

    &:hover {
      background: #9CA3AF;
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #D1D5DB transparent;
}

.loadingWrapper {
  padding: 20px 0;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resultContent {
  color: #333333;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 8px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.cursor {
  animation: blink 1s infinite;
  color: #4096FF;
  font-weight: bold;
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

.contentFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.contentStats {
  color: #999999;
  font-size: 12px;
}

.copyButton {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  font-size: 16px;
  color: #666666;
  transition: color 0.3s ease;

  &:hover {
    color: #333333;
  }
}

/* 5. 继续问输入框区域 */
.continueInputSection {
  /* 移除背景色和边框，使用分割线分隔 */
  padding: 0;
}

/* 水平布局容器 */
.continueInputHorizontal {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0;
}

/* 左侧返回按钮 - 减小占比 */
.backButton {
  display: flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  color: #666666;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
  /* 防止按钮被压缩 */
  min-width: auto;
  /* 让按钮尽可能小 */

  &:hover {
    background: #F0F0F0;
    color: #333333;
  }
}

.backIcon {
  font-size: 12px;
  color: inherit;
}

.backText {
  font-size: 13px;
  color: inherit;
}

/* 右侧输入框容器 */
.inputContainer {
  flex: 1;
}

.continueInputHorizontalInput {
  width: 100%;
  height: 36px;
  padding: 8px 12px;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 14px;
  color: #333333;
  background: white;
  font-family: inherit;
  outline: none;
  transition: border-color 0.3s ease;

  &:focus {
    border-color: #4096FF;
    box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1);
  }

  &::placeholder {
    color: #999999;
  }
}

/* 旧的样式已被新的水平布局样式替代，这些样式已不再使用 */

/* 6. 操作按钮区域 */
.actionButtons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
}

/* 常规按钮（继续问 / 调整 / 弃用 / 插入到下方） */
.actionBtn {
  height: 32px;
  padding: 0 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #666666;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: #F5F5F5;
    color: #333333;
  }
}

/* 替换原文按钮 - 蓝色主按钮 */
.primaryBtn {
  height: 32px;
  padding: 0 16px;
  background: #4285F4;
  border: none;
  border-radius: 6px;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: #3367D6;
  }
}

/* 按钮图标样式 */
.buttonIcon {
  margin-right: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
}



/* 7. 整体交互与状态 */
.modal * {
  box-sizing: border-box;
}

.thinkingToggle,
.actionBtn,
.primaryBtn,
.closeButton,
.copyButton {
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .modal {
    width: 95vw;
    margin: 10px auto;
  }

  .actionButtons {
    flex-direction: column;
    align-items: stretch;

    .actionBtn,
    .primaryBtn {
      justify-content: center;
    }
  }
}

/* 语言选择器区域样式 */
.languageSelectorSection {
  width: 50%;
  border-radius: 8px;
}