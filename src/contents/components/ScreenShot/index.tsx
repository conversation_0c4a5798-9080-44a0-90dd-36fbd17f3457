import { useEffect, useRef, useState } from 'react'
import type React from 'react'
import { message } from 'antd'
import { screenShotCancel, screenShotConfirm } from '@/src/common/images'
import { EFloatButtonActionType, EContentsMessageType } from '@src/common/const'
import { ImgIcon } from '@/src/common/Icons'
import * as styles from './index.module.less'
interface IScreenShotProps {
  removeScreenShot: () => void
}

const ScreenShot: React.FC<IScreenShotProps> = ({ removeScreenShot }) => {
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 })
  const [currentPosition, setCurrentPosition] = useState({ x: 0, y: 0 })
  const [resizePosition, setResizePosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [resizeHandle, setResizeHandle] = useState<SVGCircleElement | null>(
    null
  )
  const [originalRect, setOriginalRect] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  })
  const [viewBox, setViewBox] = useState('')
  const [maskPath, setMaskPath] = useState('')
  const [borderPath, setBorderPath] = useState('')
  const [borderPathStyle, setBorderPathStyle] = useState({})
  const [showResizeHandle, setShowResizeHandle] = useState(false)
  const [showActionWrap, setShowActionWrap] = useState(false)
  const [resizeHandlespositions, setResizeHandlespositions] = useState([
    { logicX: 0, logicY: 0, cursor: 'nw-resize', x: 0, y: 0 }, // 左上
    { logicX: 0.5, logicY: 0, cursor: 'n-resize', x: 0, y: 0 }, // 上中
    { logicX: 1, logicY: 0, cursor: 'ne-resize', x: 0, y: 0 }, // 右上
    { logicX: 1, logicY: 0.5, cursor: 'e-resize', x: 0, y: 0 }, // 右中
    { logicX: 1, logicY: 1, cursor: 'se-resize', x: 0, y: 0 }, // 右下
    { logicX: 0.5, logicY: 1, cursor: 's-resize', x: 0, y: 0 }, // 下中
    { logicX: 0, logicY: 1, cursor: 'sw-resize', x: 0, y: 0 }, // 左下
    { logicX: 0, logicY: 0.5, cursor: 'w-resize', x: 0, y: 0 }, // 左中
  ])

  const overlayRef = useRef<SVGSVGElement | null>(null)
  const maskPathRef = useRef<SVGPathElement | null>(null)

  const { x: startX, y: startY } = startPosition
  const { x: currentX, y: currentY } = currentPosition
  const { x: resizeStartX, y: resizeStartY } = resizePosition

  const getRect = () => {
    const x = Math.min(startX, currentX)
    const y = Math.min(startY, currentY)
    const w = Math.abs(currentX - startX)
    const h = Math.abs(currentY - startY)
    return { x, y, width: w, height: h }
  }

  // 更新遮罩Path的d属性
  const updateMaskPath = (rect: {
    x: number
    y: number
    width: number
    height: number
  }) => {
    const { x, y, width, height } = rect
    // 使用SVG Path创建遮罩效果：
    // 1. 外矩形：覆盖整个屏幕 (M0,0L0,heightLwidth,heightLwidth,0Z)
    // 2. 内矩形：选择区域，形成"洞" (Mx,yLx+width,yLx+width,y+heightLx,y+heightZ)
    const pathData = `M0,0L0,${window.innerHeight}L${window.innerWidth},${window.innerHeight
      }L${window.innerWidth},0Z M${x},${y}L${x + width},${y}L${x + width},${y + height
      }L${x},${y + height}Z`
    setMaskPath(pathData)
  }

  // 更新边框Path
  const updateBorderPath = (rect: {
    x: number
    y: number
    width: number
    height: number
  }) => {
    const { x, y, width, height } = rect
    const pathData = `M${x},${y}L${x + width},${y}L${x + width},${y + height
      }L${x},${y + height}Z`
    setBorderPath(pathData)
  }

  const updateSVGSelection = () => {
    const rect = getRect()
    updateMaskPath(rect)
    updateBorderPath(rect)
    setBorderPathStyle({
      display: 'block',
    })
  }

  const showResizeHandles = () => {
    setShowResizeHandle(true)
    updateResizeHandles()
  }

  const updateResizeHandles = () => {
    const rect = getRect()
    updateResizeHandlesWithRect(rect)
  }

  const updateResizeHandlesWithRect = (rect: {
    x: number
    y: number
    width: number
    height: number
  }) => {
    const positions = [
      { x: rect.x, y: rect.y }, // 左上
      { x: rect.x + rect.width / 2, y: rect.y }, // 上中
      { x: rect.x + rect.width, y: rect.y }, // 右上
      { x: rect.x + rect.width, y: rect.y + rect.height / 2 }, // 右中
      { x: rect.x + rect.width, y: rect.y + rect.height }, // 右下
      { x: rect.x + rect.width / 2, y: rect.y + rect.height }, // 下中
      { x: rect.x, y: rect.y + rect.height }, // 左下
      { x: rect.x, y: rect.y + rect.height / 2 }, // 左中
    ]

    setResizeHandlespositions(
      resizeHandlespositions.map((item, index) => {
        return {
          ...item,
          x: positions[index].x,
          y: positions[index].y,
        }
      })
    )
  }

  const onMouseDown = (e: React.MouseEvent<SVGSVGElement>) => {
    const target = e.target as SVGElement

    setShowActionWrap(false)
    if (target.tagName === 'circle') {
      // 开始调整大小
      setIsResizing(true)
      setResizeHandle(target as SVGCircleElement)
      setOriginalRect(getRect())
      setResizePosition({ x: e.clientX, y: e.clientY })
      return
    }

    setIsDragging(true)
    setStartPosition({ x: e.clientX, y: e.clientY })
    setCurrentPosition({ x: e.clientX, y: e.clientY })

    updateSVGSelection()
    showResizeHandles()
  }

  // 更新SVG选择区域
  const updateSelectionWithRect = (rect: {
    x: number
    y: number
    width: number
    height: number
  }) => {
    updateMaskPath(rect)
    updateBorderPath(rect)
    setBorderPathStyle({
      display: 'block',
    })
    updateResizeHandlesWithRect(rect)
  }

  const onMouseMove = (e: React.MouseEvent<SVGSVGElement>) => {
    if (isResizing && resizeHandle) {
      // 处理调整大小
      const deltaX = e.clientX - resizeStartX
      const deltaY = e.clientY - resizeStartY
      const position = JSON.parse(resizeHandle.dataset.position!)

      let newRect = { ...originalRect }

      if (position.logicX === 0) {
        // 左边
        newRect.x = originalRect.x + deltaX
        newRect.width = originalRect.width - deltaX
      } else if (position.logicX === 1) {
        // 右边
        newRect.width = originalRect.width + deltaX
      }

      if (position.logicY === 0) {
        // 上边
        newRect.y = originalRect.y + deltaY
        newRect.height = originalRect.height - deltaY
      } else if (position.logicY === 1) {
        // 下边
        newRect.height = originalRect.height + deltaY
      }

      // 确保最小尺寸
      if (newRect.width < 20) newRect.width = 20
      if (newRect.height < 20) newRect.height = 20

      setStartPosition({ x: newRect.x, y: newRect.y })
      setCurrentPosition({
        x: newRect.x + newRect.width,
        y: newRect.y + newRect.height,
      })
      updateSelectionWithRect(newRect)
      return
    }

    if (!isDragging) {
      return
    }
    setCurrentPosition({ x: e.clientX, y: e.clientY })
    updateSVGSelection()
    updateResizeHandles()
  }

  const onMouseUp = () => {
    if (!isDragging && !isResizing) {
      return
    }
    setIsDragging(false)
    setIsResizing(false)
    setResizeHandle(null)

    const rect = getRect()
    if (rect.width < 2 || rect.height < 2) {
      cleanup()
      return
    }
    setShowActionWrap(true)
  }

  // 更新SVG的viewBox以适应窗口大小变化
  const updateViewBox = () => {
    setViewBox(`0 0 ${window.innerWidth} ${window.innerHeight}`)
    // 如果有当前选择区域，重新计算遮罩
    if (
      maskPathRef.current &&
      (maskPathRef.current as any).style.display !== 'none'
    ) {
      const rect = getRect()
      updateMaskPath(rect)
    }
  }

  const cleanup = () => {
    window.removeEventListener('keydown', onKeyDown)
    window.removeEventListener('resize', onResize)
    if (removeScreenShot) {
      removeScreenShot()
    }
  }

  // 监听窗口大小变化
  const onResize = () => {
    updateViewBox()
  }

  const onKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      cleanup()
    }
  }

  const handleScreenShotCancel = () => {
    cleanup()
  }

  const handleDownLoad = (dataUrl: string) => {
    const link = document.createElement('a')
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    link.href = dataUrl
    link.download = `screenshot-${timestamp}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleScreenShotConfirm = () => {
    const rect = getRect()
    // 先移除/隐藏覆盖层，避免被截图捕捉到虚线与遮罩
    cleanup()
    // 等待下一帧（确保页面完成一次渲染）再触发截图
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        chrome.runtime.sendMessage(
          {
            type: EContentsMessageType.FloatingButton,
            data: {
              action: EFloatButtonActionType.Screenshot,
              rect,
              viewport: {
                width: window.innerWidth,
                height: window.innerHeight,
                dpr: window.devicePixelRatio || 1,
              },
            },
          },
          (response) => {
            const { ok, dataUrl } = response || {}
            if (!ok) {
              message.error('截图失败，请重试！')
              return
            }
            ; (async () => {
              try {
                // 复制到剪贴板（如果支持）
                if (
                  navigator.clipboard &&
                  typeof ClipboardItem !== 'undefined'
                ) {
                  const blob = await fetch(dataUrl).then((res) => res.blob())
                  const item = new ClipboardItem({ [blob.type]: blob })
                  await navigator.clipboard.write([item])
                }

                // 触发下载
                handleDownLoad(dataUrl)
                message.success('复制成功并下载截图')
              } catch (error) {
                // 如果复制失败，至少保证下载
                try {
                  handleDownLoad(dataUrl)
                  message.success('已下载截图')
                } catch (err2) {
                  message.error('下载截图失败')
                }
              }
            })()
          }
        )
      })
    })
  }

  useEffect(() => {
    setViewBox(`0 0 ${window.innerWidth} ${window.innerHeight}`)
    // 初始状态：整个屏幕都是遮罩，没有选择区域
    setMaskPath(
      `M0,0L0,${window.innerHeight}L${window.innerWidth},${window.innerHeight}L${window.innerWidth},0Z`
    )
  }, [])

  useEffect(() => {
    window.addEventListener('keydown', onKeyDown)
    window.addEventListener('resize', onResize)
    return () => {
      window.removeEventListener('keydown', onKeyDown)
      window.removeEventListener('resize', onResize)
    }
  }, [])

  return (
    <div className={styles.screenShot}>
      <svg
        viewBox={viewBox}
        style={{
          position: 'absolute',
          inset: 0,
        }}
        ref={overlayRef}
        onMouseDown={onMouseDown}
        onMouseMove={onMouseMove}
        onMouseUp={onMouseUp}
      >
        <path
          fill="black"
          fillOpacity="0.5"
          ref={maskPathRef}
          d={maskPath}
        ></path>
        <path
          fill="none"
          stroke="#0054FF"
          strokeWidth="2"
          strokeDasharray="5,5"
          style={borderPathStyle}
          d={borderPath}
        ></path>
        {resizeHandlespositions.map((positionItem, index) => {
          const { x, y, logicX, logicY, cursor } = positionItem

          return (
            <circle
              key={index}
              r="6"
              fill="#0054FF"
              stroke="white"
              strokeWidth="2"
              data-index={index}
              data-position={`{"logicX":${logicX},"logicY":${logicY}}`}
              cx={x}
              cy={y}
              style={{
                cursor,
                display: showResizeHandle ? 'block' : 'none',
                pointerEvents: 'auto',
              }}
            ></circle>
          )
        })}
      </svg>
      <div
        className={styles.actionWrap}
        style={{
          top: currentPosition.y + 10,
          left: currentPosition.x - 116,
          display: showActionWrap ? 'flex' : 'none',
          width: 116,
        }}
      >
        <ImgIcon
          src={screenShotCancel}
          width={26}
          height={26}
          onClick={handleScreenShotCancel}
        />
        <ImgIcon
          src={screenShotConfirm}
          width={26}
          height={26}
          onClick={handleScreenShotConfirm}
        />
      </div>
    </div>
  )
}

export { ScreenShot }

/**
 * plasmo框架自动将contents下的默认导出的组件注入到页面中，因此此处导出一个空组件
 * 以避免报错
 */
export default () => null
