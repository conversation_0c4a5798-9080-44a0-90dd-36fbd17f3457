.actionButtons {
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  flex-wrap: wrap;
  gap: 16px;

  &.hasBothGroups {
    justify-content: space-between;
  }

  &.rightOnly {
    justify-content: flex-end;
  }
}

.leftGroup {
  display: flex;
  align-items: center;
}

.rightGroup {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.actionBtn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: #fff;
  color: #1D222C;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:active {
    transform: translateY(1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      border-color: #d9d9d9;
      color: #666;
      background: #fff;
    }
  }
}

.primaryBtn {
  background: #1677FF;
  color: #fff;
  font-weight: 500;

  &:hover {
    color: #fff;
  }

  &:disabled {
    background: #d9d9d9;
    border-color: #d9d9d9;
    color: #fff;

    &:hover {
      background: #d9d9d9;
      border-color: #d9d9d9;
      color: #fff;
    }
  }
}



/* 响应式布局 */
@media (max-width: 480px) {
  .actionButtons {
    flex-direction: column;
    gap: 6px;
  }
  
  .actionBtn {
    width: 100%;
    justify-content: center;
  }
}
