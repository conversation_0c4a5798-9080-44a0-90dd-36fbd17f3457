import React from 'react';
import * as styles from './index.module.less';

interface LoadingProps {
  size?: 'sm' | 'default' | '2x' | '3x';
  dark?: boolean;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
}) => {

  return (
    <div className={styles.loadingContainer}>
      <div className={styles.loading}>
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
  );
};

export default Loading;
