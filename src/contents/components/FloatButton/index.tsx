import React, { useState, useRef, useEffect } from 'react'
import { Tooltip } from 'antd/es'
import { ballIcon } from '../../../common/images'
import { EFloatButtonActionType, MessageType } from '@src/common/const'
import * as styles from './index.module.less'
import useFloatingButtonPosition from '@/src/common/hooks/useFloatingButtonPosition'
import { menuItems } from '../../const'
import { getGlobalTranslationState } from '../../scripts/injectTranslate'

interface FloatingButtonProps {
  onAction: (action: EFloatButtonActionType) => void
}

const FloatingButton: React.FC<FloatingButtonProps> = (props) => {
  const { onAction } = props
  const { position, setPosition, handlePositionChange } =
    useFloatingButtonPosition()
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [isPageTranslated, setIsPageTranslated] = useState(false) // 页面翻译状态
  const buttonRef = useRef<HTMLDivElement>(null)
  const wasDraggedRef = useRef(false) // 追踪拖拽状态

  // 监听全局翻译状态变化
  useEffect(() => {
    // 初始化状态
    const globalState = getGlobalTranslationState()
    setIsPageTranslated(globalState.isPageTranslated)

    // 监听状态变化事件
    const handleStateChange = (event: CustomEvent) => {
      setIsPageTranslated(event.detail.isPageTranslated)
    }

    window.addEventListener('translationStateChanged', handleStateChange as EventListener)

    return () => {
      window.removeEventListener('translationStateChanged', handleStateChange as EventListener)
    }
  }, [])

  const handleMouseDown = (e: React.MouseEvent) => {
    // 阻止默认行为和冒泡，防止与其他事件冲突
    e.preventDefault()
    e.stopPropagation()

    wasDraggedRef.current = false // 重置拖拽标记

    const rect = buttonRef.current?.getBoundingClientRect()
    // 计算鼠标在按钮内的相对位置
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      })
    }
    setIsDragging(true)
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      wasDraggedRef.current = true // 标记为已拖拽

      // 获取窗口尺寸，更准确地计算位置
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      // 考虑拖拽偏移量，使拖拽更自然
      const newRight = windowWidth - (e.clientX + dragOffset.x)
      const newTop = e.clientY - dragOffset.y

      // 添加边界限制，防止按钮被拖出屏幕
      const buttonSize = 33 // 按钮尺寸
      const minRight = 0
      const maxRight = windowWidth - buttonSize
      const minTop = 0
      const maxTop = windowHeight - buttonSize

      // 应用边界限制
      const boundedRight = Math.max(minRight, Math.min(newRight, maxRight))
      const boundedTop = Math.max(minTop, Math.min(newTop, maxTop))

      setPosition({ right: boundedRight, top: boundedTop })
    }
  }

  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false)

      // 保存当前位置到本地存储
      handlePositionChange(position)
    }
  }

  const handleAction = (e: any, action: EFloatButtonActionType) => {
    e.stopPropagation()

    if (action === EFloatButtonActionType.Translate) {
      if (isPageTranslated) {
        // 当前是翻译状态，点击后要取消翻译
        console.log('FloatButton: 发送CANCEL_TRANSLATE消息', { currentState: isPageTranslated });

        // 方案一：通过background脚本转发（推荐）
        chrome.runtime.sendMessage({
          type: MessageType.CANCEL_TRANSLATE,
        }).then(() => {
          console.log('FloatButton: CANCEL_TRANSLATE消息发送成功')
        }).catch(error => {
          console.error('FloatButton: CANCEL_TRANSLATE消息发送失败:', error)
        })

        // 方案二：直接发送到当前页面的content script（备选）
        // window.postMessage({
        //   type: MessageType.CANCEL_TRANSLATE,
        //   source: 'floatButton'
        // }, '*')

        // 状态将通过全局状态管理器和事件监听自动更新
      } else {
        // 当前是未翻译状态，点击后要开始翻译
        console.log('FloatButton: 发送START_TRANSLATE消息', { currentState: isPageTranslated });

        onAction(action)
        // 状态将通过全局状态管理器和事件监听自动更新

      }
    } else {
      // 其他操作
      onAction(action)
    }
  }

  const handleClick = () => {
    // 在处理点击事件前，检查是否发生了拖拽
    if (wasDraggedRef.current) {
      return // 如果是拖拽，则不执行点击操作
    }
    onAction(EFloatButtonActionType.OpenPanel)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, handleMouseMove, handleMouseUp])
  return (
    <div
      ref={buttonRef}
      className={styles.floatingButton}
      style={{
        right: `${position.right}px`,
        top: `${position.top}px`,
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
    >
      <div className={styles.floatingIcon}>
        <img src={ballIcon} alt="AI助手" />
      </div>

      <div className={`${styles.floatingMenu}`}>
        {menuItems.map((item) => {
          const { title, Icon, action } = item
          // 检查是否是翻译按钮且页面已翻译
          const isTranslateActive = action === EFloatButtonActionType.Translate && isPageTranslated

          return (
            <Tooltip
              key={title}
              overlayInnerStyle={{
                minHeight: '12px',
              }}
              placement="left"
              title={
                <div
                  style={{
                    fontSize: '10px',
                  }}
                  className='floatbutton-tooltip-title'
                >
                  {isTranslateActive ? '关闭翻译' : title}
                </div>
              }
            >
              <div
                className={`${styles.floatingMenuItem} ${isTranslateActive ? styles.active : ''}`}
                onClick={(e) => handleAction(e, action)}
              >
                <Icon className={styles.menuIcon} />
              </div>
            </Tooltip>
          )
        })}
      </div>
    </div>
  )
}

const getFloatingButton = (props: FloatingButtonProps) => {
  const { onAction } = props
  return <FloatingButton onAction={onAction} />
}

export { getFloatingButton }

/**
 * plasmo框架自动将contents下的默认导出的组件注入到页面中，因此此处导出一个空组件
 * 以避免报错
 */
export default () => null
