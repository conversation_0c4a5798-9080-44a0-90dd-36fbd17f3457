.floatingButton {
  position: fixed;
  user-select: none;
  cursor: pointer;
  width: 33px;
  height: 33px;

  .floatingIcon {
    width: 33px;
    height: 33px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.08);
    background-color: #ffffff;
    border: 0.5px solid #dddee0;
    overflow: hidden;
    box-sizing: border-box;
    background-image: url('./img/icon-bg.png');
    background-size: 100% 100%;

    &:hover {
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }

    img {
      width: 100%;
      height: 100%;
    }
  }

  .floatingMenu {
    position: absolute;
    bottom: 38px;
    left: 50%;
    transform: translateX(-50%) scaleY(0);
    transform-origin: bottom center;
    width: 35px;
    border-radius: 20px;
    opacity: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #ffffff;
    box-sizing: border-box;
    transition: opacity 0.4s cubic-bezier(0.25, 1, 0.5, 1),
      transform 0.4s cubic-bezier(0.25, 1, 0.5, 1);

    .floatingMenuItem {
      width: 30px;
      height: 30px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
      box-sizing: border-box;
      cursor: pointer;
      color: #1d222c;

      &.active,
      &:hover {
        background: #e7ecff;
        color: #0958d9;
      }

      .menuIcon {
        width: 100%;
        height: 100%;
      }
    }
  }

  &:hover {
    .floatingMenu {
      opacity: 1;
      padding: 4px;
      border: 0.5px solid #dddee0;
      transform: translateX(-50%) scaleY(1);
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.08);
    }
  }
}