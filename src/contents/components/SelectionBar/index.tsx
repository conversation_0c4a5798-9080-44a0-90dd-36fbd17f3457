import React, { useState, useEffect } from 'react'
import { getMainButtons, getDropdownItems } from '../../../config/menuItems'
import type { MenuItemType } from '../../../config/menuItems'
import { chatLogo } from '../../../common/images'
import * as styles from './index.module.less'
import { getStorageData, setStorageData, STORAGE_KEYS } from '../../../common/utils/storage'


// 导入测试工具（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  import('../../utils/testAIService')
}

// 图标映射
const getIconSrc = (iconName?: string) => {
  switch (iconName) {
    case 'chatLogo':
      return chatLogo
    default:
      return null
  }
}

interface SelectionBarProps {
  selectedText: string
  onAction: (action: string) => void
  onClose: () => void
}

const SelectionBar: React.FC<SelectionBarProps> = ({
  selectedText,
  onAction,
  onClose,
}) => {
  const [showDropdown, setShowDropdown] = useState(false)
  const [showCloseOptions, setShowCloseOptions] = useState(false)
  const [isDisabled, setIsDisabled] = useState(true)

  // 在组件隐藏时重置所有状态
  useEffect(() => {
    const checkIfDisabled = async () => {
      try {
        const currentDomain = window.location.hostname
        const storageData = await getStorageData([STORAGE_KEYS.SELECTION_DISABLED_WEBSITES])
        const disabledWebsites = storageData[STORAGE_KEYS.SELECTION_DISABLED_WEBSITES] || []

        const isCurrentDomainDisabled = disabledWebsites.some(
          site => site.domain === currentDomain
        )

        if (isCurrentDomainDisabled) {
          // 如果网站被禁用，重置所有状态
          setShowDropdown(false)
          setShowCloseOptions(false)
        }

        setIsDisabled(isCurrentDomainDisabled)
      } catch (error) {
        console.error('检查禁用状态失败:', error)
      }
    }

    checkIfDisabled()
    return () => {
      setShowDropdown(false)
      setShowCloseOptions(false)
    }
  }, [selectedText])

  // 添加检查隐藏状态的逻辑
  useEffect(() => {
    const isHiddenUntilNextVisit = sessionStorage.getItem('selectionBar_hidden');
    if (isHiddenUntilNextVisit === 'true') {
      setIsDisabled(true);
    }
  });

  // 如果当前域名在禁用列表中，不渲染组件
  if (isDisabled) {
    return null
  }

  const handleAction = (action: string) => {
    // 关闭下拉菜单
    setShowDropdown(false)

    // 所有操作都通过onAction回调传递给父组件处理
    onAction(action)

    // 非面板操作需要关闭SelectionBar
    if (action !== 'open-panel') {
      onClose()
    }
  }

  const handleSettingsClick = () => {
    try {
      chrome.runtime.sendMessage({ type: 'openSettings' });
    } catch (error) {
      console.error('Failed to open settings page:', error);
    }
  }



  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
    e.nativeEvent.stopImmediatePropagation() // 阻止原生事件的立即传播
    console.log(
      'Toggling dropdown, current state:',
      showDropdown,
      'will become:',
      !showDropdown
    )
    setShowDropdown(!showDropdown)
  }

  const handleCloseOption = async (option: string) => {
    setShowCloseOptions(false);

    if (option === 'hideUntilNextVisit') {
      sessionStorage.setItem('selectionBar_hidden', 'true');
      setIsDisabled(true);
    } else if (option === 'disableOnPage') {
      try {
        // 获取当前网站域名
        const currentDomain = window.location.hostname

        // 获取现有的禁用网站列表
        const storageData = await getStorageData([STORAGE_KEYS.SELECTION_DISABLED_WEBSITES])
        const disabledWebsites = storageData[STORAGE_KEYS.SELECTION_DISABLED_WEBSITES] || []

        // 检查是否已经存在
        if (!disabledWebsites.some(site => site.domain === currentDomain)) {
          // 添加新的禁用网站
          const newWebsite = {
            id: `selection_${Date.now()}`,
            domain: currentDomain
          }

          // 保存更新后的列表
          await setStorageData({
            [STORAGE_KEYS.SELECTION_DISABLED_WEBSITES]: [...disabledWebsites, newWebsite]
          })

          console.log('网站已添加到禁用列表:', currentDomain)
        }
      } catch (error) {
        console.error('保存禁用网站失败:', error)
      }
    }

    // 关闭选择栏
    onClose();
  }

  // 渲染主要按钮
  const renderMainButton = (item: MenuItemType) => {
    const commonProps = {
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation()
        e.preventDefault()
        handleAction(item.action)
      },
      onMouseDown: (e: React.MouseEvent) => {
        e.stopPropagation()
        e.preventDefault()
      },
    }

    if (item.type === 'icon') {
      // const iconSrc = getIconSrc(item.icon)
      return (
        <div
          key={item.id}
          className={styles.selectionIcon}
          title={item.title}
          {...commonProps}
        >
          {item.icon ? (
            <img src={item.icon} alt={item.title || 'AI助手'} />
          ) : (
            // 如果没有图标，显示默认的AI图标文字
            <span className={styles.defaultIcon}>AI</span>
          )}
        </div>
      )
    }

    if (item.type === 'button') {
      // const iconSrc = getIconSrc(item.icon)
      return (
        <div key={item.id} className={styles.selectionButton} {...commonProps}>
          {item.icon && <img src={item.icon} alt={item.label} />}
          <span>{item.label}</span>
        </div>
      )
    }

    return null
  }

  return (
    <>
      <div className={styles.selectionContainer}>
        {/* 箭头指向选中文本 */}
        <div className={styles.selectionArrow} />

        <div className={styles.selectionBar}>
          {/* 渲染主要按钮 */}
          {getMainButtons().map(renderMainButton)}

          <div className={styles.selectionMore}>
            <div
              className={styles.selectionDots}
              onClick={toggleDropdown}
              onMouseDown={(e) => {
                e.stopPropagation()
                e.preventDefault()
              }}
              onMouseUp={(e) => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              ⋮
            </div>
            {showDropdown && (
              <div
                className={`${styles.selectionDropdown} ${showDropdown ? styles.show : ''
                  }`}
                onClick={(e) => e.stopPropagation()}
              >
                {getDropdownItems().map((item) => (
                  <div
                    key={item.id}
                    className={styles.selectionDropdownItem}
                    onClick={(e) => {
                      e.stopPropagation()
                      e.preventDefault()
                      handleAction(item.action)
                    }}
                    onMouseDown={(e) => {
                      e.stopPropagation()
                      e.preventDefault()
                    }}
                    onMouseUp={(e) => {
                      e.stopPropagation()
                      e.preventDefault()
                    }}
                  >
                    {item.icon && <img src={item.icon} alt={item.label} />}
                    {item.label}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 分隔线 */}
          <div className={styles.selectionDivider} />

          <div
            className={styles.selectionClose}
            onClick={(e) => {
              e.stopPropagation()
              e.preventDefault()
              setShowCloseOptions(!showCloseOptions)
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              e.preventDefault()
            }}
            title="关闭"
          >
            ×
            {showCloseOptions && (
              <div className={styles.closeOptions} onClick={e => e.stopPropagation()}>
                <div className={styles.closeOptionItem}
                  onClick={() => handleCloseOption('hideUntilNextVisit')}>
                  隐藏直到下次访问
                </div>
                <div className={styles.closeOptionItem}
                  onClick={() => handleCloseOption('disableOnPage')}>
                  在此网页时禁用
                </div>
                <div className={styles.closeOptionItem}
                  style={{ color: '#999BA0' }}>
                  在<span style={{ color: '#1677FF' }} onClick={() => { handleSettingsClick() }}>设置</span>中可以重新启用
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default SelectionBar
