.languageSelector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin: 4px 0;
}

.selectInput {
  /* 重置默认样式 */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  /* 基础样式 */
  flex: 1;
  min-width: 80px;
  height: 32px;
  padding: 4px 24px 4px 11px;
  font-size: 14px;
  line-height: 1.5714285714285714;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  /* 自定义下拉箭头 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;

  /* 字体设置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

.selectInput:hover {
  border-color: #4096ff;
}

.selectInput:focus {
  border-color: #4096ff;
  box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
}

.selectInput:disabled {
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0.04);
  border-color: #d9d9d9;
  cursor: not-allowed;
}

/* 选项样式 */
.selectInput option {
  padding: 8px 12px;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;
}

.selectInput option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.selectInput option:checked {
  background-color: #e6f4ff;
  color: #1677ff;
}

.arrowIcon {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  user-select: none;
  pointer-events: none;
  margin: 0 4px;
}
