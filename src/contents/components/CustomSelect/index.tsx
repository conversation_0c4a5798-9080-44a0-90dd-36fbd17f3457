import React, { useState, useRef, useEffect } from 'react';
import * as styles from './index.module.less';

// 辅助函数：获取事件路径，兼容不同浏览器
const getEventPath = (event: Event): EventTarget[] => {
  if (event.composedPath) {
    return event.composedPath();
  }

  // 兼容不支持 composedPath 的浏览器
  const path: EventTarget[] = [];
  let target = event.target as EventTarget | null;

  while (target) {
    path.push(target);
    const element = target as any;
    target = element.parentNode || element.host || null;
  }

  return path;
};

export interface SelectOption {
  label: string;
  value: string;
}

export interface CustomSelectProps {
  value?: string;
  onChange?: (value: string) => void;
  options?: SelectOption[];
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  value,
  onChange,
  options = [],
  placeholder = '请选择',
  disabled = false,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 获取当前选中项的标签
  const selectedOption = options.find(option => option.value === value);
  const displayText = selectedOption ? selectedOption.label : placeholder;

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      try {
        // 使用兼容性辅助函数获取事件路径
        const eventPath = getEventPath(event);
        const target = event.target as Node;

        // 检查点击是否在组件内部
        const isInsideSelect = selectRef.current && (
          selectRef.current.contains(target) ||
          eventPath.includes(selectRef.current)
        );

        const isInsideDropdown = dropdownRef.current && (
          dropdownRef.current.contains(target) ||
          eventPath.includes(dropdownRef.current)
        );

        if (!isInsideSelect && !isInsideDropdown) {
          setIsOpen(false);
        }
      } catch (error) {
        console.error('CustomSelect: Error in handleClickOutside:', error);
        // 出错时关闭下拉菜单以确保用户体验
        setIsOpen(false);
      }
    };

    if (isOpen) {
      // 使用 capture 阶段监听，确保在其他事件处理器之前执行
      document.addEventListener('mousedown', handleClickOutside, true);
      // 添加额外的 click 事件监听作为备用
      document.addEventListener('click', handleClickOutside, true);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, [isOpen]);

  // 处理选择器点击
  const handleSelectClick = (e: React.MouseEvent) => {
    // 只阻止事件冒泡，不阻止默认行为
    e.stopPropagation();
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // 处理选项点击
  const handleOptionClick = (optionValue: string, e: React.MouseEvent) => {
    // 阻止事件冒泡，避免触发外部点击处理
    e.stopPropagation();

    console.log('CustomSelect: Option clicked:', optionValue);

    try {
      if (onChange) {
        onChange(optionValue);
      }
      setIsOpen(false);
    } catch (error) {
      console.error('CustomSelect: Error in handleOptionClick:', error);
      // 即使出错也要关闭下拉菜单
      setIsOpen(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        setIsOpen(!isOpen);
        break;
      case 'Escape':
        setIsOpen(false);
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          // 可以添加键盘导航逻辑
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          // 可以添加键盘导航逻辑
        }
        break;
    }
  };

  return (
    <div className={`${styles.customSelect} ${className}`}>
      <div
        ref={selectRef}
        className={`${styles.selectTrigger} ${isOpen ? styles.open : ''} ${disabled ? styles.disabled : ''}`}
        onClick={handleSelectClick}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-disabled={disabled}
      >
        <span className={`${styles.selectValue} ${!selectedOption ? styles.placeholder : ''}`}>
          {displayText}
        </span>
        <span className={`${styles.selectArrow} ${isOpen ? styles.open : ''}`}>
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path
              d="M3 4.5L6 7.5L9 4.5"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </span>
      </div>

      {isOpen && (
        <div
          ref={dropdownRef}
          className={`${styles.selectDropdown} ${isOpen ? styles.show : ''}`}
          role="listbox"
        >
          {options.map((option) => (
            <div
              key={option.value}
              className={`${styles.selectOption} ${option.value === value ? styles.selected : ''}`}
              onClick={(e) => handleOptionClick(option.value, e)}
              onMouseDown={(e) => {
                // 在 mousedown 阶段阻止事件，确保不会被外部的 mousedown 监听器干扰
                e.stopPropagation();
              }}
              role="option"
              aria-selected={option.value === value}
            >
              {option.label}
            </div>
          ))}
          {options.length === 0 && (
            <div className={styles.selectEmpty}>暂无选项</div>
          )}
        </div>
      )}
    </div>
  );
};

export default CustomSelect;
