import TurndownService from 'turndown';
import * as gfmPlugin from 'turndown-plugin-gfm';

export type LinkStyle = 'inlined' | 'referenced' | 'discarded';

export interface LlmMarkdownOptions {
    headingStyle?: 'setext' | 'atx';
    hr?: string;
    bulletListMarker?: '-' | '+' | '*';
    emDelimiter?: '_' | '*';
    strongDelimiter?: '__' | '**';
    linkStyle?: LinkStyle;
    linkReferenceStyle?: 'full' | 'collapsed' | 'shortcut' | 'discarded';
    useGfm?: boolean | 'no-table';
    removeImages?: boolean | 'src';
}

export interface LlmMarkdownResult {
    markdown: string;
}

function getTurndown(options?: LlmMarkdownOptions) {
    const td = new TurndownService({
        headingStyle: options?.headingStyle,
        hr: options?.hr,
        bulletListMarker: options?.bulletListMarker,
        emDelimiter: options?.emDelimiter,
        strongDelimiter: options?.strongDelimiter,
        codeBlockStyle: 'fenced',
        preformattedCode: true,
    } as any);

    // Remove irrelevant/noisy nodes
    td.addRule('remove-irrelevant', {
        filter: ['meta', 'style', 'script', 'noscript', 'link', 'textarea', 'select'],
        replacement: () => ''
    });

    // Drop SVG completely
    td.addRule('svg-as-placeholder', {
        filter: 'svg' as any,
        replacement: (_content: string, node: any) => {
            const title = (node.getAttribute('aria-label') || node.getAttribute('title') || '').trim();
            const label = title ? `svg:${title}` : 'svg:graphic';
            return `\n[${label}]\n`;
        }
    });

    // Title as H1 when present
    td.addRule('title-as-h1', {
        filter: ['title'],
        replacement: (innerText: string) => `${innerText}\n===============\n`
    });

    // Improve heading cleanup (strip leading numbers/anchors artifacts)
    td.addRule('improved-heading', {
        filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
        replacement: function (content: string, node: any) {
            const lvl = Number(node.tagName?.substring(1)) || 1;
            const clean = (content || '').replace(/^[\s\u200B\uFEFF]+|[\s\u200B\uFEFF]+$/g, '');
            if ((options?.headingStyle || 'setext') === 'setext' && (lvl === 1 || lvl === 2)) {
                const underline = lvl === 1 ? '=' : '-';
                return `\n${clean}\n${underline.repeat(Math.max(3, clean.length))}\n\n`;
            }
            return `\n${'#'.repeat(Math.min(6, Math.max(1, lvl)))} ${clean}\n\n`;
        }
    });

    // Optionally remove images or collapse to src
    if (options?.removeImages) {
        const mode = options.removeImages;
        td.addRule('strip-images', {
            filter: (node: any) => node.tagName === 'IMG',
            replacement: (_content: string, node: any) => {
                if (mode === 'src') {
                    const src = (node.getAttribute('src') || '').trim();
                    if (!src) return '';
                    return `\n[image](${src})\n`;
                }
                return '';
            }
        });
    }

    // Iframe as link (preserve embedded content reference)
    td.addRule('iframe-as-link', {
        filter: (node: any) => node.tagName === 'IFRAME',
        replacement: (_content: string, node: any) => {
            const src = (node.getAttribute('src') || node.getAttribute('data-src') || '').trim();
            const title = (node.getAttribute('title') || '').trim();
            if (!src) return '';
            const label = title ? `${title}` : 'embedded content';
            return `\n[${label}](${src})\n`;
        }
    });

    // Video to markdown: prefer first <source>, fallback to src
    td.addRule('video-to-link', {
        filter: (node: any) => node.tagName === 'VIDEO',
        replacement: (_content: string, node: any) => {
            const sources = Array.from(node.querySelectorAll('source')) as any[];
            const primary = (sources.find(s => s.getAttribute('src'))?.getAttribute('src') || node.getAttribute('src') || '').trim();
            if (!primary) return '';
            const poster = (node.getAttribute('poster') || '').trim();
            const posterLine = poster ? `\n[poster](${poster})` : '';
            return `\n[video](${primary})${posterLine}\n`;
        }
    });

    // Audio to markdown: prefer first <source>, fallback to src
    td.addRule('audio-to-link', {
        filter: (node: any) => node.tagName === 'AUDIO',
        replacement: (_content: string, node: any) => {
            const sources = Array.from(node.querySelectorAll('source')) as any[];
            const primary = (sources.find(s => s.getAttribute('src'))?.getAttribute('src') || node.getAttribute('src') || '').trim();
            if (!primary) return '';
            return `\n[audio](${primary})\n`;
        }
    });

    // Figure + figcaption
    td.addRule('figure-with-caption', {
        filter: (node: any) => node.tagName === 'FIGURE',
        replacement: (content: string, node: any) => {
            const caption = (node.querySelector('figcaption')?.textContent || '').trim();
            const captionMd = caption ? `\n_${caption}_` : '';
            const cleaned = (content || '').replace(/\n{3,}/g, '\n\n').trim();
            return `\n${cleaned}${captionMd}\n`;
        }
    });

    // Summary as bold title to surface collapsible sections
    // Canvas/Object/Embed placeholders to retain context
    td.addRule('canvas-as-placeholder', {
        filter: (node: any) => node.tagName === 'CANVAS',
        replacement: () => `\n[canvas:graphic]\n`
    });
    td.addRule('embed-as-link', {
        filter: (node: any) => node.tagName === 'EMBED' || node.tagName === 'OBJECT',
        replacement: (_content: string, node: any) => {
            const src = (node.getAttribute('src') || node.getAttribute('data') || '').trim();
            if (!src) return `\n[embedded-object]\n`;
            return `\n[embedded](${src})\n`;
        }
    });
    td.addRule('summary-as-bold', {
        filter: (node: any) => node.tagName === 'SUMMARY',
        replacement: (content: string) => `\n**${(content || '').trim()}**\n`
    });

    // Configure link output style
    const linkStyle: LinkStyle = options?.linkStyle || 'inlined';
    if (linkStyle !== 'inlined') {
        // Implement referenced/shortcut/collapsed via turndown reference style configuration
        // We mimic behavior using gfm-reference style by post-processing tokens.
        // To keep this utility small, we switch to referenced links using built-in options shape.
        (td as any).options.linkStyle = 'referenced';
        (td as any).options.linkReferenceStyle = options?.linkReferenceStyle || (linkStyle === 'discarded' ? 'discarded' : 'full');
        if (linkStyle === 'discarded' || options?.linkReferenceStyle === 'discarded') {
            td.addRule('discard-links', {
                filter: 'a' as any,
                replacement: (content: string) => content
            });
        }
    }

    // GFM plugin
    if (options?.useGfm) {
        if (options.useGfm === 'no-table') {
            td.use(gfmPlugin.gfm);
            // Disable table rule by overriding to plain text
            td.addRule('no-table', {
                filter: ['table'],
                replacement: (content: string) => `\n${content}\n`
            });
        } else {
            td.use(gfmPlugin.gfm);
        }
    }

    return td;
}

export function domToLLMMarkdown(input: string | Node, options?: LlmMarkdownOptions): LlmMarkdownResult {
    const service = getTurndown(options);
    let markdown: string;
    try {
        markdown = service.turndown(input as any);
    } catch (err) {
        // If input is HTML string but not a Node, wrap minimally
        if (typeof input === 'string') {
            const wrapped = `<html><body>${input}</body></html>`;
            markdown = service.turndown(wrapped);
        } else {
            throw err;
        }
    }
    // Normalize excessive blank lines
    markdown = markdown.replace(/\n{3,}/g, '\n\n').trim() + '\n';
    return { markdown };
}
