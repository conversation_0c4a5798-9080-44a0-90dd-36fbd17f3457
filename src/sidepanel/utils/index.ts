import { isExtensionPage } from '@src/common/utils'
import { EIP_CONFIG } from '@src/common/const'
import { fetchAndStoreWikiAuth } from '@src/common/utils/wikiAuth'

// 响应格式转换函数：将新接口响应格式转换为原有的老格式
const transformNewApiToOldFormat = (newData: any) => {
  const result = newData.resultData || newData

  return {
    "userId": result.userId || "",  // resultData.userId → userId
    "userName": result.userName || "",  // resultData.userName → userName
    "accessToken": "",  // 新接口没有此字段，设为空
    "smallPhotoUrl": "",
    "largePhotoUrl": "",
    "mobile": "18900000000", // 默认值，新接口可能没有这个字段
    "landlineTelehpone": "025-00000000", // 默认值
    "email": result.userId ? `${result.userId}@htzq.htsc.com.cn` : "", // 根据userId生成邮箱
    "pinyinName": null,
    "hrState": null,
    "state": "1",
    "userHrPositions": [
      {
        "userId": result.userId || "",  // 保持一致
        "userName": result.userName || "",  // 保持一致
        "jobCode": "6692", // 默认值，新接口可能没有这些字段
        "jobName": "前端开发工程师",
        "deptCode": "ZZ001014",
        "deptName": "信息技术部",
        "jobLevel": null,
        "positionType": "0",
        "primaryPostion": "HR_PRIMARY_POSITION",
        "workCity": ""
      }
    ],
    "userType": "1"
  }
}

export const getCurrentTab = async (messageApi?: any) => {
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
  const currentTab = tabs[0]
  if (isExtensionPage(currentTab.url)) {
    messageApi?.open({
      type: 'error',
      content: '不支持Chrome内置页面，请在其他页面尝试',
    })
    return
  }
  return currentTab
}

// 检查登录状态函数
export const checkLoginStatus = (callback: (isLoggedIn: boolean, data: any) => void) => {
  chrome.cookies.get({ url: EIP_CONFIG.BASE_URL + '/', name: EIP_CONFIG.TOKEN_NAME }, function (cookie) {
    console.log('cookie is ', cookie)
    if (cookie && cookie.value) {
      // 判断是否为生产环境，使用不同的接口
      const isProd = process.env.PLASMO_TAG === 'prod'
      const apiUrl = isProd && EIP_CONFIG.CHECK_LOGIN_API ? EIP_CONFIG.CHECK_LOGIN_API : EIP_CONFIG.AUTH_API
      const method = 'POST' // 所有环境都使用POST请求

      console.log(`使用${isProd ? '生产' : '测试'}环境接口: ${apiUrl}, 方法: ${method}`)

      fetch(apiUrl, {
        method: method,
        credentials: 'include',
        headers: {
          'Cookie': `${EIP_CONFIG.TOKEN_NAME}=` + cookie.value
        }
      })
        .then(response => {
          // 获取响应体内容
          return response.json().then(data => {
            console.log('响应体内容:', data);
            return { response, data };
          }).catch(err => {
            // console.error('解析响应失败:', err);
            return { response, data: null };
          });
        })
        .then(async (result) => {
          // 根据环境判断成功状态
          let isSuccess = false
          if (isProd && EIP_CONFIG.CHECK_LOGIN_API) {
            // 生产环境新接口：检查code为"0"表示成功
            isSuccess = result.response.ok && result.data &&
              (result.data.code === "0" || result.data.code === 0)
          } else {
            // 测试环境老接口：response.ok和有数据表示成功
            isSuccess = result.response.ok && result.data
          }

          if (isSuccess) {
            console.log('登录验证成功:', result.data)

            // 如果是生产环境的新接口，需要转换新格式为老格式
            let transformedData = result.data
            if (isProd && EIP_CONFIG.CHECK_LOGIN_API) {
              // 生产环境：新接口返回新格式，转换为老格式
              transformedData = transformNewApiToOldFormat(result.data)
              console.log('生产环境新接口数据转换为老格式:', transformedData)
            } else {
              // 测试环境：老接口直接返回老格式，无需转换
              console.log('测试环境使用老接口，数据格式保持不变')
            }

            // EIP验证成功后，异步获取并存储wiki认证信息（不阻塞主流程）
            // fetchAndStoreWikiAuth().catch(error => {
            //   console.error('获取Wiki认证信息失败，但不影响EIP登录:', error)
            // })

            callback(true, transformedData);
          } else {
            callback(false, null);
          }
        })
        .catch(err => {
          console.error('登录验证失败:', err);
          callback(false, null);
        });
    } else {
      callback(false, null);
    }
  });
}
