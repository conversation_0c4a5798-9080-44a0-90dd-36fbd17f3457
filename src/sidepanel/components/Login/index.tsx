import { message, But<PERSON> } from 'antd/es'
import { useNavigate } from 'react-router-dom'
import { chatLogo } from '@src/common/images'
import { EIP_CONFIG } from '@src/common/const'
import './style.less'

export default () => {
  const [messageApi, contextHolder] = message.useMessage()

  const handleLogin = async () => {
    // 获取当前活跃的tab ID并保存
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    const currentTab = tabs[0]

    if (currentTab && currentTab.id) {
      // 保存当前tab ID到storage，供登录成功后使用
      await chrome.storage.session.set({ 'originalTabId': currentTab.id })
      console.log('保存原始tab ID:', currentTab.id)
    }

    // 打开登录页面
    chrome.tabs.create({ url: EIP_CONFIG.LOGIN_PAGE })
  }

  return (
    <div className="login">
      <img src={chatLogo} className="chat-logo" />
      <Button
        type="primary"
        size="large"
        className="login-btn"
        onClick={handleLogin}
      >
        登录
      </Button>
      {contextHolder}
    </div>
  )
}
