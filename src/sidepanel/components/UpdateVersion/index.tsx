import React from 'react'
import { update } from '@src/common/images'
import { getBaseUrl } from '@src/common/utils/baseUrl'
import './style.less'

const UpdateVersion: React.FC = () => {
  const handleDownloadClick = async () => {
    try {
      const baseUrl = getBaseUrl()
      const downloadUrl = `${baseUrl}${baseUrl.includes('eiplite') ? '' : '/webassist'}/webAssistant/index.html#/download`;
      console.log('downloadUrl', downloadUrl)
      await chrome.tabs.create({
        url: downloadUrl,
      })
    } catch (error) {
      console.error('Failed to open download page:', error)
      console.error('Download page navigation failed')
    }
  }
  return (
    <div className="updateVersionContainer">
      <img className="updateImg" src={update} alt="update" />
      <div className="title">插件版本更新</div>
      <div className="downloadBtn" onClick={handleDownloadClick}>点击下载</div>
    </div>
  )
}

export default UpdateVersion
